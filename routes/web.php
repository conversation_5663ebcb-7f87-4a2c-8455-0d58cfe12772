<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\NewsController;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    // Get featured news (only 1)
    $featuredNews = \App\Models\News::published()->featured()->first();

    // Get other published news (excluding featured)
    $otherNews = \App\Models\News::published()
        ->when($featuredNews, function ($query) use ($featuredNews) {
            return $query->where('id', '!=', $featuredNews->id);
        })
        ->orderBy('created_at', 'desc')
        ->limit(4)
        ->get();

    return view('index', compact('featuredNews', 'otherNews'));
})->name('home');

Route::get('/test-data', function () {
    return view('test-data');
})->name('test-data');

Route::get('/test-video', function () {
    return view('test-video');
})->name('test-video');

Route::post('/test-appointment', function (Request $request) {
    Log::info('Test appointment request:', $request->all());

    return response()->json([
        'success' => true,
        'message' => 'Test successful',
        'received_data' => $request->all(),
        'birth_year_type' => gettype($request->birth_year),
        'birth_year_value' => $request->birth_year
    ]);
})->name('test-appointment');

Route::get('/test-admin-dashboard', function () {
    $recentAppointments = \App\Models\Appointment::orderBy('created_at', 'desc')->take(5)->get();

    return response()->json([
        'success' => true,
        'appointments_count' => $recentAppointments->count(),
        'appointments' => $recentAppointments->map(function ($appointment) {
            return [
                'id' => $appointment->id,
                'name' => $appointment->name,
                'appointment_date' => $appointment->appointment_date,
                'appointment_time' => $appointment->appointment_time,
                'status' => $appointment->status
            ];
        })
    ]);
})->name('test-admin-dashboard');

Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{slug}', [NewsController::class, 'show'])->name('news.show');

Auth::routes();

Route::get('/home', [\App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest admin routes (login)
    Route::middleware('guest:admin')->group(function () {
        Route::get('/login', [\App\Http\Controllers\Admin\AuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [\App\Http\Controllers\Admin\AuthController::class, 'login']);
    });

    // Protected admin routes
    Route::middleware('admin.auth')->group(function () {
        Route::post('/logout', [\App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');

        // Dashboard
        Route::get('/', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index']);

        // Profile Management
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/change-password', [\App\Http\Controllers\Admin\ProfileController::class, 'showChangePasswordForm'])->name('change-password');
            Route::post('/change-password', [\App\Http\Controllers\Admin\ProfileController::class, 'changePassword']);
        });

        // Appointments Management
        Route::prefix('appointments')->name('appointments.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AppointmentController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\AppointmentController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\AppointmentController::class, 'store'])->name('store');
            Route::get('/{id}', [\App\Http\Controllers\Admin\AppointmentController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\AppointmentController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\AppointmentController::class, 'update'])->name('update');
            Route::put('/{id}/status', [\App\Http\Controllers\Admin\AppointmentController::class, 'updateStatus'])->name('updateStatus');
            Route::get('/{id}/confirm', [\App\Http\Controllers\Admin\AppointmentController::class, 'confirm'])->name('confirm');
            Route::get('/{id}/cancel', [\App\Http\Controllers\Admin\AppointmentController::class, 'cancel'])->name('cancel');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\AppointmentController::class, 'destroy'])->name('destroy');
        });

        // Simplified route names for appointments
        Route::get('/appointments', [\App\Http\Controllers\Admin\AppointmentController::class, 'index'])->name('appointments');

        // Questions Management
        Route::prefix('questions')->name('questions.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\QuestionController::class, 'index'])->name('index');
            Route::get('/{id}', [\App\Http\Controllers\Admin\QuestionController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\QuestionController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\QuestionController::class, 'update'])->name('update');
            Route::post('/{id}/answer', [\App\Http\Controllers\Admin\QuestionController::class, 'answer'])->name('answer');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\QuestionController::class, 'destroy'])->name('destroy');
        });

        // Simplified route names for questions
        Route::get('/questions', [\App\Http\Controllers\Admin\QuestionController::class, 'index'])->name('questions');

        // News Management
        Route::prefix('news')->name('news.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\NewsController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\NewsController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\NewsController::class, 'store'])->name('store');
            Route::post('/upload-image', [\App\Http\Controllers\Admin\NewsController::class, 'uploadImage'])->name('upload-image');
            Route::get('/{id}', [\App\Http\Controllers\Admin\NewsController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\NewsController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\NewsController::class, 'update'])->name('update');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\NewsController::class, 'destroy'])->name('delete');
        });

        // Simplified route names for news
        Route::get('/news', [\App\Http\Controllers\Admin\NewsController::class, 'index'])->name('news');

        // Categories Management
        Route::prefix('categories')->name('categories.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('store');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('update');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('delete');
            Route::post('/update-order', [\App\Http\Controllers\Admin\CategoryController::class, 'updateOrder'])->name('update-order');
            Route::get('/trashed', [\App\Http\Controllers\Admin\CategoryController::class, 'trashed'])->name('trashed');
            Route::post('/{id}/restore', [\App\Http\Controllers\Admin\CategoryController::class, 'restore'])->name('restore');
            Route::delete('/{id}/force-delete', [\App\Http\Controllers\Admin\CategoryController::class, 'forceDelete'])->name('force-delete');
        });

        // Simplified route names for categories
        Route::get('/categories', [\App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('categories');

        // Medical Topics Management
        Route::prefix('medical-topics')->name('medical-topics.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'store'])->name('store');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'update'])->name('update');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'destroy'])->name('delete');
            Route::post('/update-order', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'updateOrder'])->name('update-order');
            Route::get('/trashed', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'trashed'])->name('trashed');
            Route::post('/{id}/restore', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'restore'])->name('restore');
            Route::delete('/{id}/force-delete', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'forceDelete'])->name('force-delete');
        });

        // Simplified route names for medical topics
        Route::get('/medical-topics', [\App\Http\Controllers\Admin\MedicalTopicController::class, 'index'])->name('medical-topics');

        // Videos Management (to be implemented)
        Route::prefix('videos')->name('videos.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\VideoController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\VideoController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\VideoController::class, 'store'])->name('store');
            Route::get('/{id}', [\App\Http\Controllers\Admin\VideoController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [\App\Http\Controllers\Admin\VideoController::class, 'edit'])->name('edit');
            Route::put('/{id}', [\App\Http\Controllers\Admin\VideoController::class, 'update'])->name('update');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\VideoController::class, 'destroy'])->name('destroy');
        });

        // Simplified route names for videos
        Route::get('/videos', [\App\Http\Controllers\Admin\VideoController::class, 'index'])->name('videos');

        // Settings Management
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('index');
            Route::put('/', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('update');
            Route::delete('/banner', [\App\Http\Controllers\Admin\SettingController::class, 'deleteBannerImage'])->name('delete-banner');
        });

        // Simplified route names for settings
        Route::get('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings');

        // Appointment Settings Management
        Route::prefix('appointment-settings')->name('appointment-settings.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AppointmentSettingsController::class, 'index'])->name('index');
            Route::put('/', [\App\Http\Controllers\Admin\AppointmentSettingsController::class, 'update'])->name('update');
        });

        // Simplified route names for appointment settings
        Route::get('/appointment-settings', [\App\Http\Controllers\Admin\AppointmentSettingsController::class, 'index'])->name('appointment-settings');

        // Daily Time Slots Management
        Route::prefix('daily-time-slots')->name('daily-time-slots.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\DailyTimeSlotController::class, 'index'])->name('index');
            Route::put('/', [\App\Http\Controllers\Admin\DailyTimeSlotController::class, 'update'])->name('update');
            Route::put('/bulk-update', [\App\Http\Controllers\Admin\DailyTimeSlotController::class, 'bulkUpdate'])->name('bulk-update');
            Route::post('/clone-week', [\App\Http\Controllers\Admin\DailyTimeSlotController::class, 'cloneWeek'])->name('clone-week');
        });

        // Simplified route names for daily time slots
        Route::get('/daily-time-slots', [\App\Http\Controllers\Admin\DailyTimeSlotController::class, 'index'])->name('daily-time-slots');
    });
});
