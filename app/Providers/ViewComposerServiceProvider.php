<?php

namespace App\Providers;

use App\Http\View\Composers\GlobalDataComposer;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register view composers for frontend views
        View::composer([
            'components.app-layout',
            'components.banner',
            'components.body',
            'components.footer',
            'home',
            'index',
        ], GlobalDataComposer::class);
    }
}
