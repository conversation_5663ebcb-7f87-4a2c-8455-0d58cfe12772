<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Appointment;
use App\Models\DailyTimeSlot;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AppointmentController extends Controller
{
    /**
     * Get available dates and time slots
     */
    public function getAvailableSlots()
    {
        $availableDates = [];
        $now = Carbon::now();

        // Get the start of the week (Monday) that contains today
        $startOfWeek = $now->copy()->startOfWeek(Carbon::MONDAY);

        // Generate exactly 7 days from Monday to Sunday
        for ($dayOffset = 0; $dayOffset < 7; $dayOffset++) {
            $date = $startOfWeek->copy()->addDays($dayOffset);

            // Get available time slots for this date
            $timeSlots = DailyTimeSlot::forDate($date->format('Y-m-d'))
                ->available()
                ->orderBy('time_slot')
                ->get();

            $availableTimes = [];

            foreach ($timeSlots as $slot) {
                $appointmentDateTime = $date->copy()->setTimeFromTimeString($slot->time_slot);

                // Only include time slots that are in the future (date + time > current date + time)
                if ($appointmentDateTime->isPast()) {
                    continue;
                }

                // Count existing appointments for this slot
                $existingCount = Appointment::where('appointment_date', $date->format('Y-m-d'))
                    ->where('appointment_time', $slot->time_slot)
                    ->where('status', '!=', 'cancelled')
                    ->count();

                $isAvailable = $existingCount < $slot->max_appointments;
                $remaining = $slot->max_appointments - $existingCount;

                $availableTimes[] = [
                    'value' => $slot->time_slot,
                    'label' => $slot->time_slot,
                    'available' => $isAvailable,
                    'remaining' => $remaining
                ];
            }

            // Always add the date, even if no time slots are available
            // This ensures we show exactly 7 days (Monday to Sunday) with disabled buttons for unavailable days
            $availableDates[] = [
                'value' => $date->format('Y-m-d'),
                'day' => $this->getVietnameseDayName($date->dayOfWeek),
                'date' => $date->format('d/m'),
                'times' => $availableTimes,
                'hasAvailableSlots' => !empty($availableTimes) && collect($availableTimes)->some('available', true)
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'dates' => $availableDates
            ]
        ]);
    }

    /**
     * Store a new appointment
     */
    public function store(Request $request)
    {
        // Log incoming request data
        Log::info('Appointment creation request:', $request->all());

        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'birth_year' => 'required|string|size:4|regex:/^\d{4}$/',
                'phone' => 'required|string|max:20',
                'appointment_date' => 'required|date|after_or_equal:today',
                'appointment_time' => 'required|string',
                'notes' => 'nullable|string|max:1000',
                'status_type' => 'nullable|string|in:new,returning'
            ]);

            // Additional validation for birth year range
            $currentYear = date('Y');
            $birthYear = (int) $validated['birth_year'];
            if ($birthYear < ($currentYear - 100) || $birthYear > $currentYear) {
                return response()->json([
                    'success' => false,
                    'message' => 'Năm sinh không hợp lệ. Vui lòng nhập năm sinh từ ' . ($currentYear - 100) . ' đến ' . $currentYear . '.',
                    'errors' => ['birth_year' => ['Năm sinh không hợp lệ']]
                ], 422);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ.',
                'errors' => $e->errors()
            ], 422);
        }

        try {
            // Check if the time slot exists and is available
            $timeSlot = DailyTimeSlot::forDate($request->appointment_date)
                ->where('time_slot', $request->appointment_time)
                ->available()
                ->first();

            if (!$timeSlot) {
                return response()->json([
                    'success' => false,
                    'message' => 'Khung giờ này không khả dụng. Vui lòng chọn khung giờ khác.'
                ], 422);
            }

            // Check if the time slot is full
            $existingCount = Appointment::where('appointment_date', $request->appointment_date)
                ->where('appointment_time', $request->appointment_time)
                ->where('status', '!=', 'cancelled')
                ->count();

            if ($existingCount >= $timeSlot->max_appointments) {
                return response()->json([
                    'success' => false,
                    'message' => 'Khung giờ này đã đầy. Vui lòng chọn khung giờ khác.'
                ], 422);
            }

            // Create appointment using validated data
            $appointment = Appointment::create([
                'name' => trim($validated['name']),
                'birth_year' => $validated['birth_year'],
                'phone' => trim($validated['phone']),
                'appointment_date' => $validated['appointment_date'],
                'appointment_time' => $validated['appointment_time'],
                'notes' => isset($validated['notes']) ? trim($validated['notes']) : null,
                'status' => 'pending',
                'status_type' => $validated['status_type'] ?? 'new'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Đặt lịch hẹn thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.',
                'data' => $appointment
            ]);
        } catch (\Exception $e) {
            Log::error('Appointment creation error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đặt lịch hẹn. Vui lòng thử lại.'
            ], 500);
        }
    }

    /**
     * Get Vietnamese day name
     */
    private function getVietnameseDayName($dayOfWeek)
    {
        $days = [
            Carbon::MONDAY => 'T2',
            Carbon::TUESDAY => 'T3',
            Carbon::WEDNESDAY => 'T4',
            Carbon::THURSDAY => 'T5',
            Carbon::FRIDAY => 'T6',
            Carbon::SATURDAY => 'T7',
            Carbon::SUNDAY => 'CN'
        ];

        return $days[$dayOfWeek] ?? '';
    }
}
