<?php

namespace App\Http\Controllers;

use App\Models\News;

class NewsController extends Controller
{
    public function index()
    {
        $news = News::published()
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('news.index', compact('news'));
    }

    public function show($slug)
    {
        $article = News::where('slug', $slug)
            ->published()
            ->firstOrFail();

        $other_news = News::published()
            ->where('id', '!=', $article->id)
            ->orderBy('created_at', 'desc')
            ->limit(4)
            ->get();

        return view('news.show', compact('article', 'other_news'));
    }
}
