<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Appointment;
use App\Models\News;
use App\Models\Question;
use App\Models\Video;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Basic statistics
        $todayAppointments = Appointment::today()->count();
        $totalAppointments = Appointment::count();
        $pendingAppointments = Appointment::pending()->count();
        $confirmedAppointments = Appointment::confirmed()->count();

        $newQuestions = Question::unanswered()->count();
        $totalQuestions = Question::count();
        $answeredQuestions = Question::answered()->count();

        $totalNews = News::count();
        $publishedNews = News::where('status', 'published')->count();
        $draftNews = News::where('status', 'draft')->count();

        $totalVideos = Video::count();
        $publishedVideos = Video::where('status', 'published')->count();
        $featuredVideos = Video::where('featured', true)->count();

        // Recent data
        $recentAppointments = Appointment::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $recentQuestions = Question::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $recentNews = News::with('admin')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $recentVideos = Video::with('admin')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Weekly statistics for charts
        $weeklyAppointments = $this->getWeeklyAppointments();
        $weeklyQuestions = $this->getWeeklyQuestions();

        // Monthly statistics
        $monthlyStats = $this->getMonthlyStats();

        return view('admin.dashboard', compact(
            'todayAppointments',
            'totalAppointments',
            'pendingAppointments',
            'confirmedAppointments',
            'newQuestions',
            'totalQuestions',
            'answeredQuestions',
            'totalNews',
            'publishedNews',
            'draftNews',
            'totalVideos',
            'publishedVideos',
            'featuredVideos',
            'recentAppointments',
            'recentQuestions',
            'recentNews',
            'recentVideos',
            'weeklyAppointments',
            'weeklyQuestions',
            'monthlyStats'
        ));
    }

    /**
     * Get weekly appointments data for charts
     *
     * @return array
     */
    private function getWeeklyAppointments()
    {
        $data = [];
        $labels = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('d/m');
            $data[] = Appointment::whereDate('created_at', $date->toDateString())->count();
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * Get weekly questions data for charts
     *
     * @return array
     */
    private function getWeeklyQuestions()
    {
        $data = [];
        $labels = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->format('d/m');
            $data[] = Question::whereDate('created_at', $date->toDateString())->count();
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * Get monthly statistics
     *
     * @return array
     */
    private function getMonthlyStats()
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        $currentMonthAppointments = Appointment::where('created_at', '>=', $currentMonth)->count();
        $lastMonthAppointments = Appointment::whereBetween('created_at', [
            $lastMonth,
            $lastMonth->copy()->endOfMonth()
        ])->count();

        $currentMonthQuestions = Question::where('created_at', '>=', $currentMonth)->count();
        $lastMonthQuestions = Question::whereBetween('created_at', [
            $lastMonth,
            $lastMonth->copy()->endOfMonth()
        ])->count();

        return [
            'appointments' => [
                'current' => $currentMonthAppointments,
                'last' => $lastMonthAppointments,
                'change' => $lastMonthAppointments > 0 ?
                    round((($currentMonthAppointments - $lastMonthAppointments) / $lastMonthAppointments) * 100, 1) : 0
            ],
            'questions' => [
                'current' => $currentMonthQuestions,
                'last' => $lastMonthQuestions,
                'change' => $lastMonthQuestions > 0 ?
                    round((($currentMonthQuestions - $lastMonthQuestions) / $lastMonthQuestions) * 100, 1) : 0
            ]
        ];
    }
}
