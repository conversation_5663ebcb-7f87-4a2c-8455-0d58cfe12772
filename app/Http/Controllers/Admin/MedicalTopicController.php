<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MedicalTopic;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MedicalTopicController extends Controller
{
    /**
     * Display a listing of the medical topics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = MedicalTopic::with('category');

        // Filter by status
        if ($request->has('status') && $request->status != 'all') {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->has('category_id') && $request->category_id != 'all') {
            $query->where('category_id', $request->category_id);
        }

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $topics = $query->ordered()->paginate(10);
        $categories = Category::active()->ordered()->get();

        return view('admin.medical-topics.index', compact('topics', 'categories'));
    }

    /**
     * Show the form for creating a new medical topic.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::active()->ordered()->get();
        return view('admin.medical-topics.form', compact('categories'));
    }

    /**
     * Store a newly created medical topic in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:medical_topics',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'featured' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set default sort order if not provided
        if (empty($validated['sort_order'])) {
            $validated['sort_order'] = MedicalTopic::max('sort_order') + 1;
        }

        // Handle featured checkbox
        $validated['featured'] = $request->has('featured');

        MedicalTopic::create($validated);

        return redirect()->route('admin.medical-topics')
            ->with('success', 'Chủ đề y tế đã được tạo thành công.');
    }

    /**
     * Show the form for editing the specified medical topic.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $topic = MedicalTopic::findOrFail($id);
        $categories = Category::active()->ordered()->get();

        return view('admin.medical-topics.form', compact('topic', 'categories'));
    }

    /**
     * Update the specified medical topic in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $topic = MedicalTopic::findOrFail($id);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:medical_topics,slug,' . $topic->id,
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'featured' => 'boolean',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle featured checkbox
        $validated['featured'] = $request->has('featured');

        $topic->update($validated);

        return redirect()->route('admin.medical-topics')
            ->with('success', 'Chủ đề y tế đã được cập nhật thành công.');
    }

    /**
     * Remove the specified medical topic from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $topic = MedicalTopic::findOrFail($id);

        $topic->delete(); // This will soft delete

        return redirect()->route('admin.medical-topics')
            ->with('success', 'Chủ đề y tế đã được xóa thành công.');
    }

    /**
     * Update the sort order of medical topics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'topics' => 'required|array',
            'topics.*.id' => 'required|integer|exists:medical_topics,id',
            'topics.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['topics'] as $topicData) {
            MedicalTopic::where('id', $topicData['id'])
                ->update(['sort_order' => $topicData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }
}
