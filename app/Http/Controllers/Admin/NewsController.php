<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    /**
     * Display a listing of the news.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = News::query();

        // Filter by status
        if ($request->has('status') && $request->status != 'all') {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%")
                    ->orWhere('category', 'like', "%{$search}%");
            });
        }

        $news = $query->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.news.index', compact('news'));
    }

    /**
     * Show the form for creating a new news.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::active()->ordered()->get();
        return view('admin.news.form', compact('categories'));
    }

    /**
     * Store a newly created news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Check if POST size was exceeded
        if (empty($_POST) && empty($_FILES) && $_SERVER['CONTENT_LENGTH'] > 0) {
            $displayMaxSize = ini_get('post_max_size');
            return redirect()->back()->withErrors([
                'file_size' => "Kích thước file quá lớn. Giới hạn tối đa: {$displayMaxSize}. Vui lòng chọn file nhỏ hơn."
            ]);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news',
            'header_content' => 'nullable|string',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'category' => 'required|string|max:255',
            'type' => 'required|in:post,video',
            'youtube_link' => 'required_if:type,video|nullable|url',
            'status' => 'required|in:draft,published',
            'featured' => 'boolean',
            'excerpt' => 'nullable|string|max:500',
        ], [
            'featured_image.max' => 'Hình ảnh chính không được vượt quá 5MB.',
            'featured_image.image' => 'File phải là hình ảnh.',
            'thumbnail.max' => 'Hình ảnh thumbnail không được vượt quá 5MB.',
            'thumbnail.image' => 'File phải là hình ảnh.',
            'youtube_link.required_if' => 'Link YouTube là bắt buộc khi loại tin tức là video.',
            'youtube_link.url' => 'Link YouTube phải là URL hợp lệ.',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Generate excerpt if not provided
        if (empty($validated['excerpt'])) {
            $text = !empty($validated['header_content']) ? $validated['header_content'] : $validated['content'];
            $validated['excerpt'] = Str::limit(strip_tags($text), 150);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $file = $request->file('featured_image');
            $filename = 'news-featured-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/news'), $filename);
            $validated['featured_image'] = 'images/news/' . $filename;
        }

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $file = $request->file('thumbnail');
            $filename = 'news-thumb-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/news'), $filename);
            $validated['thumbnail'] = 'images/news/' . $filename;
        }

        // Handle featured checkbox
        $validated['featured'] = $request->has('featured');

        // Handle YouTube link for video type
        if ($validated['type'] === 'video' && !empty($validated['youtube_link'])) {
            $validated['video_url'] = $validated['youtube_link'];
        }
        unset($validated['youtube_link']); // Remove youtube_link as it's not in the database

        // Set admin_id
        $validated['admin_id'] = Auth::guard('admin')->id();

        News::create($validated);

        return redirect()->route('admin.news')
            ->with('success', 'Bài viết đã được tạo thành công.');
    }

    /**
     * Show the form for editing the specified news.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $news = News::findOrFail($id);
        $categories = Category::active()->ordered()->get();

        return view('admin.news.form', compact('news', 'categories'));
    }

    /**
     * Update the specified news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $news = News::findOrFail($id);

        // Check if POST size was exceeded
        if (empty($_POST) && empty($_FILES) && $_SERVER['CONTENT_LENGTH'] > 0) {
            $displayMaxSize = ini_get('post_max_size');
            return redirect()->back()->withErrors([
                'file_size' => "Kích thước file quá lớn. Giới hạn tối đa: {$displayMaxSize}. Vui lòng chọn file nhỏ hơn."
            ]);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug,' . $news->id,
            'header_content' => 'nullable|string',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'category' => 'required|string|max:255',
            'type' => 'required|in:post,video',
            'youtube_link' => 'required_if:type,video|nullable|url',
            'status' => 'required|in:draft,published',
            'featured' => 'boolean',
            'excerpt' => 'nullable|string|max:500',
        ], [
            'featured_image.max' => 'Hình ảnh chính không được vượt quá 5MB.',
            'featured_image.image' => 'File phải là hình ảnh.',
            'thumbnail.max' => 'Hình ảnh thumbnail không được vượt quá 5MB.',
            'thumbnail.image' => 'File phải là hình ảnh.',
            'youtube_link.required_if' => 'Link YouTube là bắt buộc khi loại tin tức là video.',
            'youtube_link.url' => 'Link YouTube phải là URL hợp lệ.',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Generate excerpt if not provided
        if (empty($validated['excerpt'])) {
            $text = !empty($validated['header_content']) ? $validated['header_content'] : $validated['content'];
            $validated['excerpt'] = Str::limit(strip_tags($text), 150);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old featured image if exists
            if ($news->featured_image && file_exists(public_path($news->featured_image))) {
                unlink(public_path($news->featured_image));
            }

            $file = $request->file('featured_image');
            $filename = 'news-featured-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/news'), $filename);
            $validated['featured_image'] = 'images/news/' . $filename;
        }

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($news->thumbnail && file_exists(public_path($news->thumbnail))) {
                unlink(public_path($news->thumbnail));
            }

            $file = $request->file('thumbnail');
            $filename = 'news-thumb-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/news'), $filename);
            $validated['thumbnail'] = 'images/news/' . $filename;
        }

        // Handle featured checkbox
        $validated['featured'] = $request->has('featured');

        // Handle YouTube link for video type
        if ($validated['type'] === 'video' && !empty($validated['youtube_link'])) {
            $validated['video_url'] = $validated['youtube_link'];
        } elseif ($validated['type'] === 'post') {
            $validated['video_url'] = null; // Clear video_url for post type
        }
        unset($validated['youtube_link']); // Remove youtube_link as it's not in the database

        $news->update($validated);

        return redirect()->route('admin.news')
            ->with('success', 'Bài viết đã được cập nhật thành công.');
    }

    /**
     * Remove the specified news from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $news = News::findOrFail($id);

        // Delete image if exists
        if ($news->image && file_exists(public_path($news->image))) {
            unlink(public_path($news->image));
        }

        $news->delete();

        return redirect()->route('admin.news')
            ->with('success', 'Bài viết đã được xóa thành công.');
    }

    /**
     * Handle image upload for text editor.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'upload' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
        ]);

        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            $filename = 'editor-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/news/editor'), $filename);

            $url = asset('images/news/editor/' . $filename);

            return response()->json([
                'uploaded' => true,
                'url' => $url
            ]);
        }

        return response()->json([
            'uploaded' => false,
            'error' => [
                'message' => 'Không thể tải lên hình ảnh.'
            ]
        ]);
    }
}
