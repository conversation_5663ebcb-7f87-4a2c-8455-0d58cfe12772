<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class SettingController extends Controller
{
    /**
     * Display the settings page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $bannerSettings = Setting::getByGroup('banner');
        $generalSettings = Setting::getByGroup('general');

        return view('admin.settings.index', compact('bannerSettings', 'generalSettings'));
    }

    /**
     * Update the settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        // Check if POST size was exceeded
        if (empty($_POST) && empty($_FILES) && $_SERVER['CONTENT_LENGTH'] > 0) {
            $displayMaxSize = ini_get('post_max_size');
            return redirect()->back()->withErrors([
                'file_size' => "Kích thước file quá lớn. Giới hạn tối đa: {$displayMaxSize}. Vui lòng chọn file nhỏ hơn."
            ]);
        }

        $request->validate([
            'banner_desktop_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
            'banner_mobile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB
        ], [
            'banner_desktop_image.max' => 'Hình ảnh banner desktop không được vượt quá 5MB.',
            'banner_mobile_image.max' => 'Hình ảnh banner mobile không được vượt quá 5MB.',
            'banner_desktop_image.image' => 'File banner desktop phải là hình ảnh.',
            'banner_mobile_image.image' => 'File banner mobile phải là hình ảnh.',
        ]);

        // Handle banner desktop image upload
        if ($request->hasFile('banner_desktop_image')) {
            $file = $request->file('banner_desktop_image');
            $filename = 'banner-desktop-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/banners'), $filename);

            // Delete old image if exists
            $oldImage = Setting::get('banner_desktop_image');
            if ($oldImage && File::exists(public_path($oldImage))) {
                File::delete(public_path($oldImage));
            }

            Setting::set(
                'banner_desktop_image',
                'images/banners/' . $filename,
                'image',
                'banner',
                'Banner Desktop Image',
                'Desktop version of the banner image'
            );
        }

        // Handle banner mobile image upload
        if ($request->hasFile('banner_mobile_image')) {
            $file = $request->file('banner_mobile_image');
            $filename = 'banner-mobile-' . time() . '.' . $file->getClientOriginalExtension();
            $file->move(public_path('images/banners'), $filename);

            // Delete old image if exists
            $oldImage = Setting::get('banner_mobile_image');
            if ($oldImage && File::exists(public_path($oldImage))) {
                File::delete(public_path($oldImage));
            }

            Setting::set(
                'banner_mobile_image',
                'images/banners/' . $filename,
                'image',
                'banner',
                'Banner Mobile Image',
                'Mobile version of the banner image'
            );
        }

        // Handle other text settings
        $textSettings = [
            'site_title' => ['label' => 'Site Title', 'group' => 'general'],
            'site_description' => ['label' => 'Site Description', 'group' => 'general'],
            'banner_title' => ['label' => 'Banner Title', 'group' => 'banner'],
            'banner_subtitle' => ['label' => 'Banner Subtitle', 'group' => 'banner'],
        ];

        foreach ($textSettings as $key => $config) {
            if ($request->has($key)) {
                Setting::set(
                    $key,
                    $request->input($key),
                    'text',
                    $config['group'],
                    $config['label']
                );
            }
        }

        return redirect()->route('admin.settings')
            ->with('success', 'Cài đặt đã được cập nhật thành công.');
    }

    /**
     * Delete a banner image.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deleteBannerImage(Request $request)
    {
        $request->validate([
            'type' => 'required|in:desktop,mobile',
        ]);

        $settingKey = 'banner_' . $request->type . '_image';
        $imagePath = Setting::get($settingKey);

        if ($imagePath && File::exists(public_path($imagePath))) {
            File::delete(public_path($imagePath));
        }

        Setting::where('key', $settingKey)->delete();

        return redirect()->route('admin.settings')
            ->with('success', 'Hình ảnh banner đã được xóa thành công.');
    }
}
