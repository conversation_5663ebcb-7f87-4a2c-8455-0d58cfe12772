<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $settings = $this->getSettings();
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:500',
            'facebook_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'working_hours' => 'nullable|string|max:255',
            'appointment_slots' => 'nullable|string',
            'site_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'site_favicon' => 'nullable|image|mimes:ico,png|max:1024',
            'about_content' => 'nullable|string',
            'services_content' => 'nullable|string',
            'meta_keywords' => 'nullable|string|max:500',
            'meta_description' => 'nullable|string|max:160',
            'google_analytics_id' => 'nullable|string|max:50',
            'facebook_pixel_id' => 'nullable|string|max:50',
        ]);

        // Handle file uploads
        if ($request->hasFile('site_logo')) {
            // Delete old logo if exists
            $oldLogo = $this->getSetting('site_logo');
            if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                Storage::disk('public')->delete($oldLogo);
            }
            
            $validated['site_logo'] = $request->file('site_logo')->store('settings', 'public');
        }

        if ($request->hasFile('site_favicon')) {
            // Delete old favicon if exists
            $oldFavicon = $this->getSetting('site_favicon');
            if ($oldFavicon && Storage::disk('public')->exists($oldFavicon)) {
                Storage::disk('public')->delete($oldFavicon);
            }
            
            $validated['site_favicon'] = $request->file('site_favicon')->store('settings', 'public');
        }

        // Save settings
        foreach ($validated as $key => $value) {
            $this->setSetting($key, $value);
        }

        // Clear cache
        Cache::forget('site_settings');

        return redirect()->route('admin.settings')
            ->with('success', 'Cài đặt đã được cập nhật thành công.');
    }

    /**
     * Get all settings
     *
     * @return array
     */
    private function getSettings()
    {
        return Cache::remember('site_settings', 3600, function () {
            $settingsFile = storage_path('app/settings.json');
            
            if (file_exists($settingsFile)) {
                return json_decode(file_get_contents($settingsFile), true) ?: [];
            }
            
            return $this->getDefaultSettings();
        });
    }

    /**
     * Get a specific setting
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    private function getSetting($key, $default = null)
    {
        $settings = $this->getSettings();
        return $settings[$key] ?? $default;
    }

    /**
     * Set a specific setting
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    private function setSetting($key, $value)
    {
        $settings = $this->getSettings();
        $settings[$key] = $value;
        
        $settingsFile = storage_path('app/settings.json');
        file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));
    }

    /**
     * Get default settings
     *
     * @return array
     */
    private function getDefaultSettings()
    {
        return [
            'site_name' => 'BS Lý Đại Lương',
            'site_description' => 'Phòng khám chuyên khoa',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '',
            'contact_address' => '',
            'facebook_url' => '',
            'youtube_url' => '',
            'working_hours' => 'Thứ 2 - Thứ 6: 8:00 - 17:00',
            'appointment_slots' => '08:00,09:00,10:00,11:00,14:00,15:00,16:00',
            'about_content' => '',
            'services_content' => '',
            'meta_keywords' => '',
            'meta_description' => '',
            'google_analytics_id' => '',
            'facebook_pixel_id' => '',
        ];
    }
}
