<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QuestionController extends Controller
{
    /**
     * Display a listing of the questions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Question::query();
        
        // Filter by status
        if ($request->has('status') && $request->status != 'all') {
            $query->where('status', $request->status);
        }
        
        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('question', 'like', "%{$search}%");
            });
        }
        
        $questions = $query->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('admin.questions.index', compact('questions'));
    }

    /**
     * Display the specified question.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $question = Question::findOrFail($id);
        
        return view('admin.questions.show', compact('question'));
    }

    /**
     * Show the form for editing the specified question.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $question = Question::findOrFail($id);
        
        return view('admin.questions.edit', compact('question'));
    }

    /**
     * Update the specified question in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $question = Question::findOrFail($id);
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'question' => 'required|string',
            'answer' => 'nullable|string',
        ]);
        
        // If answer is provided and question is not answered yet
        if (!empty($validated['answer']) && $question->status === 'unanswered') {
            $validated['status'] = 'answered';
            $validated['answered_by'] = Auth::guard('admin')->user()->name;
            $validated['answered_at'] = now();
        }
        
        $question->update($validated);
        
        return redirect()->route('admin.questions')
            ->with('success', 'Câu hỏi đã được cập nhật thành công.');
    }

    /**
     * Remove the specified question from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $question = Question::findOrFail($id);
        $question->delete();
        
        return redirect()->route('admin.questions')
            ->with('success', 'Câu hỏi đã được xóa thành công.');
    }

    /**
     * Answer the specified question.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function answer(Request $request, $id)
    {
        $question = Question::findOrFail($id);
        
        $validated = $request->validate([
            'answer' => 'required|string',
        ]);
        
        $question->update([
            'answer' => $validated['answer'],
            'status' => 'answered',
            'answered_by' => Auth::guard('admin')->user()->name,
            'answered_at' => now(),
        ]);
        
        return redirect()->route('admin.questions.show', $question->id)
            ->with('success', 'Câu hỏi đã được trả lời thành công.');
    }
}