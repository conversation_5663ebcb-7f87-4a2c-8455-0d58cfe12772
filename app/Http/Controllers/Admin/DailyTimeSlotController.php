<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DailyTimeSlot;
use Carbon\Carbon;

class DailyTimeSlotController extends Controller
{
    public function index(Request $request)
    {
        // Generate 30 days from today (including Sundays)
        $dates = [];
        for ($i = 0; $i < 30; $i++) {
            $date = Carbon::today()->addDays($i);
            $dates[] = $date;
        }

        // Get all possible time slots (7:00 to 22:00, 30-minute intervals)
        $allTimeSlots = $this->generateAllTimeSlots();

        // Prepare data for view
        $datesData = [];
        foreach ($dates as $date) {
            $dateStr = $date->format('Y-m-d');

            // Get existing slots for this specific date
            $existingSlots = DailyTimeSlot::where('date', $dateStr)->get();
            $existingSlotsArray = [];
            foreach ($existingSlots as $slot) {
                $existingSlotsArray[$slot->time_slot] = $slot;
            }

            $timeSlots = [];
            foreach ($allTimeSlots as $timeSlot) {
                $timeSlots[] = [
                    'time_slot' => $timeSlot,
                    'is_available' => isset($existingSlotsArray[$timeSlot]) ? $existingSlotsArray[$timeSlot]->is_available : false,
                ];
            }

            $datesData[] = [
                'date' => $dateStr,
                'date_formatted' => $date->format('d/m'),
                'day_name' => $date->locale('vi')->shortDayName,
                'is_today' => $date->isToday(),
                'time_slots' => $timeSlots
            ];
        }

        return view('admin.daily-time-slots.index', compact('datesData', 'allTimeSlots'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'slots' => 'required|array',
            'slots.*' => 'required|array',
            'slots.*.date' => 'required|date',
            'slots.*.time_slots' => 'array',
            'slots.*.time_slots.*' => 'string'
        ]);

        foreach ($request->slots as $dateSlots) {
            $date = $dateSlots['date'];
            $timeSlots = $dateSlots['time_slots'] ?? [];

            // Delete existing slots for this date first
            DailyTimeSlot::where('date', $date)->forceDelete();

            // Create new slots
            foreach ($timeSlots as $timeSlot) {
                // Use updateOrCreate to avoid duplicates
                DailyTimeSlot::updateOrCreate(
                    [
                        'date' => $date,
                        'time_slot' => $timeSlot
                    ],
                    [
                        'is_available' => true,
                        'max_appointments' => 2
                    ]
                );
            }
        }

        return redirect()->back()->with('success', 'Cài đặt khung giờ đã được cập nhật thành công!');
    }

    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'time_slots' => 'required|array',
            'time_slots.*' => 'required|string',
            'apply_to_all' => 'boolean'
        ]);

        if ($request->apply_to_all) {
            // Apply to all 30 days (including Sundays)
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::today()->addDays($i);

                // Delete existing slots for this date first
                DailyTimeSlot::where('date', $date->format('Y-m-d'))->forceDelete();

                // Create new slots
                foreach ($request->time_slots as $timeSlot) {
                    DailyTimeSlot::updateOrCreate(
                        [
                            'date' => $date->format('Y-m-d'),
                            'time_slot' => $timeSlot
                        ],
                        [
                            'is_available' => true,
                            'max_appointments' => 2
                        ]
                    );
                }
            }
        }

        return redirect()->back()->with('success', 'Cài đặt khung giờ đã được áp dụng cho tất cả ngày!');
    }

    public function cloneWeek(Request $request)
    {
        $request->validate([
            'source_date' => 'required|date'
        ]);

        $sourceDate = Carbon::parse($request->source_date);
        $today = Carbon::now();

        // Get source time slots for the selected date
        $sourceSlots = DailyTimeSlot::forDate($sourceDate->format('Y-m-d'))
            ->available()
            ->get();

        if ($sourceSlots->isEmpty()) {
            return redirect()->back()->with('error', 'Ngày nguồn không có lịch khám để sao chép!');
        }

        // Clone schedule for 14 days starting from today
        for ($dayOffset = 0; $dayOffset < 14; $dayOffset++) {
            $targetDate = $today->copy()->addDays($dayOffset);

            // Delete existing slots for target date first
            DailyTimeSlot::where('date', $targetDate->format('Y-m-d'))->forceDelete();

            // Clone each time slot from source date
            foreach ($sourceSlots as $sourceSlot) {
                DailyTimeSlot::create([
                    'date' => $targetDate->format('Y-m-d'),
                    'time_slot' => $sourceSlot->time_slot,
                    'is_available' => $sourceSlot->is_available,
                    'max_appointments' => $sourceSlot->max_appointments
                ]);
            }
        }

        return redirect()->back()->with(
            'success',
            'Đã sao chép lịch từ ngày ' . $sourceDate->format('d/m/Y') .
                ' cho 14 ngày kể từ hôm nay (' . $today->format('d/m/Y') . ') thành công!'
        );
    }

    private function generateAllTimeSlots()
    {
        $slots = [];
        for ($hour = 7; $hour <= 22; $hour++) {
            foreach (['00', '30'] as $minute) {
                $slots[] = sprintf('%02d:%s', $hour, $minute);
            }
        }
        return $slots;
    }
}
