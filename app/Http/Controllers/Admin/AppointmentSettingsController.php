<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;

class AppointmentSettingsController extends Controller
{
    public function index()
    {
        $timeSlots = json_decode(Setting::where('key', 'appointment_time_slots')->value('value') ?? '[]');
        $maxPerSlot = Setting::where('key', 'max_appointments_per_slot')->value('value') ?? '2';
        $daysAhead = Setting::where('key', 'appointment_days_ahead')->value('value') ?? '7';

        return view('admin.appointment-settings.index', compact('timeSlots', 'maxPerSlot', 'daysAhead'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'time_slots' => 'required|array',
            'time_slots.*' => 'required|string|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
            'max_per_slot' => 'required|integer|min:1|max:10',
            'days_ahead' => 'required|integer|min:1|max:30'
        ]);

        // Update time slots
        Setting::updateOrCreate(
            ['key' => 'appointment_time_slots'],
            ['value' => json_encode($request->time_slots)]
        );

        // Update max appointments per slot
        Setting::updateOrCreate(
            ['key' => 'max_appointments_per_slot'],
            ['value' => $request->max_per_slot]
        );

        // Update days ahead
        Setting::updateOrCreate(
            ['key' => 'appointment_days_ahead'],
            ['value' => $request->days_ahead]
        );

        return redirect()->back()->with('success', 'Cài đặt lịch hẹn đã được cập nhật thành công!');
    }
}
