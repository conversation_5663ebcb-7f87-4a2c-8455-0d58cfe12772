<?php

namespace App\Http\View\Composers;

use App\Models\Category;
use App\Models\MedicalTopic;
use Illuminate\View\View;

class GlobalDataComposer
{
    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        // Get active categories ordered by sort_order
        $categories = Category::active()->ordered()->get();
        
        // Get active medical topics with their categories, ordered by sort_order
        $medicalTopics = MedicalTopic::with('category')
            ->active()
            ->ordered()
            ->get();
        
        // Get featured medical topics
        $featuredMedicalTopics = MedicalTopic::with('category')
            ->active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();
        
        $view->with([
            'globalCategories' => $categories,
            'globalMedicalTopics' => $medicalTopics,
            'globalFeaturedMedicalTopics' => $featuredMedicalTopics,
        ]);
    }
}
