<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class News extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'news';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'header_content',
        'featured_image',
        'content',
        'thumbnail',
        'category',
        'type',
        'video_url',
        'status',
        'featured',
        'excerpt',
        'admin_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'featured' => 'boolean',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function ($news) {
            if (empty($news->slug)) {
                $news->slug = Str::slug($news->title);
            }

            if (empty($news->excerpt)) {
                // Generate excerpt from header_content first, then content if header_content is empty
                $text = !empty($news->header_content) ? $news->header_content : $news->content;
                $news->excerpt = Str::limit(strip_tags($text), 150);
            }
        });

        static::updating(function ($news) {
            if ($news->isDirty('title') && empty($news->slug)) {
                $news->slug = Str::slug($news->title);
            }

            if (($news->isDirty('content') || $news->isDirty('header_content')) && empty($news->excerpt)) {
                // Generate excerpt from header_content first, then content if header_content is empty
                $text = !empty($news->header_content) ? $news->header_content : $news->content;
                $news->excerpt = Str::limit(strip_tags($text), 150);
            }
        });
    }

    /**
     * Get the admin that owns the news.
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * Get the category that owns the news.
     */
    public function categoryModel()
    {
        return $this->belongsTo(Category::class, 'category', 'slug');
    }

    /**
     * Scope a query to only include published news.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include draft news.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope a query to only include featured news.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope a query to only include post type news.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePost($query)
    {
        return $query->where('type', 'post');
    }

    /**
     * Scope a query to only include video type news.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVideo($query)
    {
        return $query->where('type', 'video');
    }

    /**
     * Get YouTube video ID from URL
     *
     * @return string|null
     */
    public function getYoutubeVideoIdAttribute()
    {
        if (!$this->video_url) {
            return null;
        }

        preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->video_url, $matches);
        return isset($matches[1]) ? $matches[1] : null;
    }

    /**
     * Get YouTube thumbnail URL
     *
     * @return string|null
     */
    public function getYoutubeThumbnailAttribute()
    {
        $videoId = $this->youtube_video_id;
        return $videoId ? "https://img.youtube.com/vi/{$videoId}/maxresdefault.jpg" : null;
    }
}
