<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class DailyTimeSlot extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'date',
        'time_slot',
        'is_available',
        'max_appointments'
    ];

    protected $casts = [
        'date' => 'date',
        'is_available' => 'boolean'
    ];

    /**
     * Get appointments for this time slot
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'appointment_date', 'date')
            ->where('appointment_time', $this->time_slot)
            ->where('status', '!=', 'cancelled');
    }

    /**
     * Check if this time slot is full
     */
    public function isFull()
    {
        return $this->appointments()->count() >= $this->max_appointments;
    }

    /**
     * Get remaining slots
     */
    public function getRemainingSlots()
    {
        return $this->max_appointments - $this->appointments()->count();
    }

    /**
     * Scope for available slots
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }
}
