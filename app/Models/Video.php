<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Video extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'youtube_url',
        'youtube_id',
        'thumbnail',
        'category',
        'status',
        'admin_id',
        'views',
        'featured',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'featured' => 'boolean',
        'views' => 'integer',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function ($video) {
            if (empty($video->youtube_id) && !empty($video->youtube_url)) {
                $video->youtube_id = self::extractYouTubeId($video->youtube_url);
            }

            if (empty($video->thumbnail) && !empty($video->youtube_id)) {
                $video->thumbnail = "https://img.youtube.com/vi/{$video->youtube_id}/maxresdefault.jpg";
            }
        });

        static::updating(function ($video) {
            if ($video->isDirty('youtube_url') && !empty($video->youtube_url)) {
                $video->youtube_id = self::extractYouTubeId($video->youtube_url);
                $video->thumbnail = "https://img.youtube.com/vi/{$video->youtube_id}/maxresdefault.jpg";
            }
        });
    }

    /**
     * Extract YouTube video ID from URL
     *
     * @param string $url
     * @return string|null
     */
    public static function extractYouTubeId($url)
    {
        $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
        preg_match($pattern, $url, $matches);
        return isset($matches[1]) ? $matches[1] : null;
    }

    /**
     * Get the admin that owns the video.
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * Scope a query to only include published videos.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include featured videos.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Get the embed URL for the video
     *
     * @return string|null
     */
    public function getEmbedUrlAttribute()
    {
        if ($this->youtube_id) {
            return "https://www.youtube.com/embed/{$this->youtube_id}";
        }
        return null;
    }

    /**
     * Get the watch URL for the video
     *
     * @return string|null
     */
    public function getWatchUrlAttribute()
    {
        if ($this->youtube_id) {
            return "https://www.youtube.com/watch?v={$this->youtube_id}";
        }
        return null;
    }

    /**
     * Increment the view count
     */
    public function incrementViews()
    {
        $this->increment('views');
    }
}
