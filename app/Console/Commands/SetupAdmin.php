<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use App\Models\Admin;

class SetupAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:setup {--fresh : Drop all tables and recreate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the admin panel with default data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Setting up Admin Panel...');

        if ($this->option('fresh')) {
            $this->warn('This will drop all existing data!');
            if ($this->confirm('Are you sure you want to continue?')) {
                $this->info('Running fresh migrations...');
                Artisan::call('migrate:fresh');
                $this->info(Artisan::output());
            } else {
                $this->info('Setup cancelled.');
                return 0;
            }
        } else {
            $this->info('Running migrations...');
            Artisan::call('migrate');
            $this->info(Artisan::output());
        }

        $this->info('Seeding admin data...');
        Artisan::call('db:seed', ['--class' => 'AdminSeeder']);
        $this->info(Artisan::output());

        $this->info('Creating storage link...');
        Artisan::call('storage:link');
        $this->info(Artisan::output());

        $this->newLine();
        $this->info('✅ Admin Panel setup completed successfully!');
        $this->newLine();

        // Display login credentials
        $this->displayCredentials();

        return 0;
    }

    /**
     * Display the default login credentials
     */
    private function displayCredentials()
    {
        $this->info('🔑 Default Admin Login Credentials:');
        $this->newLine();

        $admins = [
            [
                'name' => 'BS Lý Đại Lương',
                'email' => '<EMAIL>',
                'password' => 'admin123456'
            ],
            [
                'name' => 'Admin System',
                'email' => '<EMAIL>',
                'password' => 'system123456'
            ],
            [
                'name' => 'Demo Admin',
                'email' => '<EMAIL>',
                'password' => 'demo123456'
            ]
        ];

        foreach ($admins as $admin) {
            $this->line("👤 <info>{$admin['name']}</info>");
            $this->line("   📧 Email: <comment>{$admin['email']}</comment>");
            $this->line("   🔒 Password: <comment>{$admin['password']}</comment>");
            $this->newLine();
        }

        $this->warn('⚠️  IMPORTANT: Please change these default passwords after first login!');
        $this->newLine();
        $this->info('🌐 Admin Panel URL: ' . url('/admin/login'));
        $this->newLine();
    }
}
