const defaultTheme = require("tailwindcss/defaultTheme");

module.exports = {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: ["Inter"],
            },
            colors: {
                primary: "#014B7F",
                secondary: "#777E90",
                highlight: "#FFCF23",
                backgroundLight: "#F1F9FC",
                lightYellow: "#FFF1D5",
                paleBlue: "#DBF0FF",
                yellowSoft: "#FFEB99",
                lightGrayBg: "#F8F8F9",
                lightBorderBlue: "#BEDAE5",
                textPrimary: "#141416",
                grayCool: "#94A3B8",
                grayLight: "#E2E8F0",
                formBlue: "#014B7F",
                bgTime: "#E3F2F7",
                textTime: "#004B7F",
                //border category
                borderCate1: "#FFDEAC",
                backgroundCate1: "#FFF4E3",

                borderCate2: "#6FE8CE",
                backgroundCate2: "#C2FFF2",

                borderCate3: "#83E3F2",
                backgroundCate3: "#B8F5FF",

                borderCate4: "#AECDFF",
                backgroundCate4: "#DAE8FF",

                borderCate5: "#99EC9C",
                backgroundCate5: "#C7FFC9",

                borderCate6: "#DFBEFF",
                backgroundCate6: "#F3E6FF",
            },
        },
    },
    variants: {},
    plugins: [require("@tailwindcss/forms")],
};
