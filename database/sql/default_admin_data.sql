-- Default Admin Data for BS Lý Đại Lư<PERSON>
-- Run this SQL after creating the admins table

-- Insert default admin accounts
INSERT INTO `admins` (`name`, `email`, `email_verified_at`, `password`, `created_at`, `updated_at`) VALUES
('BS Lý Đại Lương', '<EMAIL>', NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()),
('Admin System', '<EMAIL>', NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW()),
('Demo Admin', '<EMAIL>', NOW(), '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW());

-- Note: The password hash above corresponds to 'password'
-- For security, you should change these passwords after first login

-- Default passwords for reference:
-- <EMAIL> -> admin123456
-- <EMAIL> -> system123456  
-- <EMAIL> -> demo123456

-- To generate new password hashes in Laravel, use:
-- Hash::make('your_password')
-- Or in tinker: php artisan tinker, then Hash::make('your_password')

-- Alternative with proper password hashes:
-- You can replace the above INSERT with these if you want the actual passwords:

/*
INSERT INTO `admins` (`name`, `email`, `email_verified_at`, `password`, `created_at`, `updated_at`) VALUES
('BS Lý Đại Lương', '<EMAIL>', NOW(), '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', NOW(), NOW()),
('Admin System', '<EMAIL>', NOW(), '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', NOW(), NOW()),
('Demo Admin', '<EMAIL>', NOW(), '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', NOW(), NOW());
*/

-- The above hashes correspond to the actual passwords mentioned in the comments
