<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            [
                'name' => 'BÉO PHÌ',
                'background_color' => '#FFF4E3',
                'border_color' => '#FFDEAC',
                'sort_order' => 1,
            ],
            [
                'name' => 'ĐÁI THÁO ĐƯỜNG',
                'background_color' => '#C2FFF2',
                'border_color' => '#6FE8CE',
                'sort_order' => 2,
            ],
            [
                'name' => 'TUYẾN GIÁP',
                'background_color' => '#B8F5FF',
                'border_color' => '#83E3F2',
                'sort_order' => 3,
            ],
            [
                'name' => 'HỘI CHỨNG BUỒNG TRỨNG ĐA NANG',
                'background_color' => '#DAE8FF',
                'border_color' => '#AECDFF',
                'sort_order' => 4,
            ],
            [
                'name' => 'LOÃNG XƯƠNG',
                'background_color' => '#C7FFC9',
                'border_color' => '#99EC9C',
                'sort_order' => 5,
            ],
            [
                'name' => 'RỐI LOẠN NỘI TIẾT - CHUYỂN HÓA',
                'background_color' => '#F3E6FF',
                'border_color' => '#DFBEFF',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => 'Danh mục ' . $categoryData['name'],
                'background_color' => $categoryData['background_color'],
                'border_color' => $categoryData['border_color'],
                'status' => 'active',
                'sort_order' => $categoryData['sort_order'],
            ]);
        }
    }
}
