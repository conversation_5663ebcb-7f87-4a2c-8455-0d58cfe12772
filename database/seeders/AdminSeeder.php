<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create default admin account
        Admin::create([
            'name' => 'BS Lý Đ<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123456'),
            'email_verified_at' => now(),
        ]);

        // Create additional admin accounts if needed
        Admin::create([
            'name' => 'Admin System',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123456'),
            'email_verified_at' => now(),
        ]);

        // Create demo admin account for testing
        Admin::create([
            'name' => 'Demo Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('demo123456'),
            'email_verified_at' => now(),
        ]);
    }
}
