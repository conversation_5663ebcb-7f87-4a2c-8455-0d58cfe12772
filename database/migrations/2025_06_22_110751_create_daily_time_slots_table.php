<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDailyTimeSlotsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('daily_time_slots', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->string('time_slot'); // e.g., "09:00"
            $table->boolean('is_available')->default(true);
            $table->integer('max_appointments')->default(2);
            $table->timestamps();
            $table->softDeletes();

            // Unique constraint to prevent duplicate date-time combinations
            $table->unique(['date', 'time_slot']);

            // Index for faster queries
            $table->index(['date', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('daily_time_slots');
    }
}
