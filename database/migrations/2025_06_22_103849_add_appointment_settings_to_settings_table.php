<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddAppointmentSettingsToSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Insert default appointment time slots settings
        DB::table('settings')->insert([
            [
                'key' => 'appointment_time_slots',
                'value' => json_encode([
                    '07:00',
                    '07:30',
                    '08:00',
                    '08:30',
                    '09:00',
                    '09:30',
                    '10:00',
                    '10:30',
                    '11:00',
                    '11:30',
                    '14:00',
                    '14:30',
                    '15:00',
                    '15:30',
                    '16:00',
                    '16:30',
                    '17:00',
                    '17:30',
                    '18:00',
                    '18:30',
                    '19:00',
                    '19:30',
                    '20:00',
                    '20:30',
                    '21:00',
                    '21:30',
                    '22:00',
                    '22:30',
                    '23:00',
                    '23:30',
                ]),
                'type' => 'json',
                'group' => 'appointment',
                'label' => 'Khung Giờ <PERSON>h<PERSON>',
                'description' => '<PERSON><PERSON><PERSON> khung giờ mà bệnh nhân có thể đặt lịch hẹn',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'max_appointments_per_slot',
                'value' => '2',
                'type' => 'text',
                'group' => 'appointment',
                'label' => 'Số Lượng Tối Đa Mỗi Khung Giờ',
                'description' => 'Số lượng cuộc hẹn tối đa cho mỗi khung giờ',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'appointment_days_ahead',
                'value' => '7',
                'type' => 'text',
                'group' => 'appointment',
                'label' => 'Số Ngày Cho Phép Đặt Trước',
                'description' => 'Số ngày tối đa mà bệnh nhân có thể đặt lịch trước',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('settings')->whereIn('key', [
            'appointment_time_slots',
            'max_appointments_per_slot',
            'appointment_days_ahead'
        ])->delete();
    }
}
