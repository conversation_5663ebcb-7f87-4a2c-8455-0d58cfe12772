<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('news', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('header_content')->nullable(); // Rich text header content
            $table->string('featured_image')->nullable(); // Main image below header
            $table->text('content'); // Rich text main content
            $table->string('thumbnail')->nullable(); // Thumbnail for list view
            $table->string('category');
            $table->enum('type', ['post', 'video'])->default('post'); // Type: post or video
            $table->string('video_url')->nullable(); // YouTube video URL
            $table->enum('status', ['draft', 'published'])->default('draft');
            $table->boolean('featured')->default(false);
            $table->text('excerpt')->nullable();
            $table->foreignId('admin_id')->constrained('admins');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news');
    }
}
