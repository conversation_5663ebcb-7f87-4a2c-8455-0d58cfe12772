@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom styles for medical website */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
}

/* Custom background for the banner search input */
.banner-search-bg {
    background: linear-gradient(0deg, #F8F8F9, #F8F8F9),
        linear-gradient(0deg, #F8F8F9, #F8F8F9),
        linear-gradient(0deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.6)),
        linear-gradient(0deg, #FFFFFF, #FFFFFF);
}

@layer components {
    .btn-primary {
        @apply py-3 px-6 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75 transition duration-300;
    }

    .btn-secondary {
        @apply py-3 px-6 bg-white text-blue-600 border border-blue-600 font-semibold rounded-lg shadow-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75 transition duration-300;
    }

    .medical-card {
        @apply bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300 border border-gray-100;
    }

    .medical-icon {
        @apply w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4;
    }

    .section-title {
        @apply text-3xl font-bold text-gray-800 mb-4 text-center;
    }

    .section-subtitle {
        @apply text-gray-600 max-w-2xl mx-auto text-center mb-12;
    }

    .nav-link {
        @apply text-gray-700 hover:text-blue-600 font-medium transition duration-300 relative;
    }

    .nav-link:hover::after {
        content: '';
        @apply absolute bottom-0 left-0 w-full h-0.5 bg-blue-600;
    }

    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .medical-gradient {
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    }

    .hero-decoration {
        @apply absolute rounded-full opacity-20;
        background: linear-gradient(45deg, #3b82f6, #60a5fa);
    }

    .floating-animation {
        animation: float 6s ease-in-out infinite;
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }
}

@layer utilities {
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
}

/* Custom animations */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Responsive design enhancements */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-padding {
        padding: 3rem 0;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for better accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

@media (min-width: 768px) {
    #GROUP629 {
        width: 51.5941px;
        height: 154px;
    }
}

#GROUP629 {
    right: 0px;
    bottom: 0px;
    position: fixed;
    z-index: 90000050;
}

#GROUP629,
#POPUP27 {
    top: auto;
    left: auto;
}

[data-action="true"] {
    cursor: pointer;
}


.ladi-group {
    position: absolute;
    width: 100%;
    height: 100%;
}

/* @media (max-width: 767px) {

    #SHAPE150,
    #SHAPE153,
    #SHAPE154 {
        width: 39.9175px;
        height: 39.9175px;
    }
} */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}