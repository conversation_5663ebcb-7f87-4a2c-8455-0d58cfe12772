@extends('layouts.app')

@section('head')
<!-- Plyr CSS -->
<link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
@endsection

@section('content')
@php
$featuredNews = \App\Models\News::published()->featured()->first();

// Get other published news (excluding featured)
$otherNews = \App\Models\News::published()
->when($featuredNews, function ($query) use ($featuredNews) {
return $query->where('id', '!=', $featuredNews->id);
})
->orderBy('created_at', 'desc')
->paginate(7);
@endphp
<div class="bg-backgroundLight">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-5xl mx-auto">
            <!-- Back button & Breadcrumbs -->
            <div class="mb-8">
                <a href="javascript:history.back()"
                    class="w-10 h-10 flex items-center justify-center bg-black text-white rounded-lg mb-4 hover:bg-gray-800 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12H5m5-5l-5 5 5 5">
                        </path>
                    </svg>
                </a>
                <div class="text-sm">
                    <a href="{{ url('/') }}" class="font-medium text-secondary hover:text-primary">Trang chủ</a>
                    <span class="mx-2 text-secondary">></span>
                    <span class="text-secondary/60">Tin tức</span>
                </div>
            </div>

            <section class="py-16 bg-backgroundLight">
                <div class="container mx-auto px-4">
                    <div class="max-w-5xl mx-auto">
                        <!-- Section Header -->
                        <div class="text-center mb-12">
                            <h2 class="text-4xl font-bold text-primary mb-4">Tin Tức</h2>
                            <p class="text-secondary text-lg max-w-3xl mx-auto">
                                Cập nhật về các vấn đề liên quan đến sức khỏe trong lĩnh vực y tế tốt nhất
                            </p>
                        </div>

                        <!-- News Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 lg:gap-7 gap-3 mb-12">
                            @if($featuredNews)
                            <!-- Featured Article -->
                            <article
                                class="p-4 lg:p-5 group col-span-2 flex flex-col md:flex-row bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                @if($featuredNews->type == 'video')
                                <div class="md:w-1/2 h-64 md:h-auto relative cursor-pointer video-trigger"
                                    data-video-url="{{ $featuredNews->video_url }}"
                                    data-video-title="{{ $featuredNews->title }}"
                                    data-video-date="{{ $featuredNews->created_at->format('d/m/Y') }}"
                                    data-video-time="{{ $featuredNews->created_at->diffForHumans() }}"
                                    data-video-excerpt="{{ $featuredNews->excerpt }}">
                                    <img src="{{ asset($featuredNews->thumbnail ?? $featuredNews->featured_image ?? '/images/news/1.jpg') }}"
                                        alt="{{ $featuredNews->title }}" class="w-full h-full object-cover rounded-2xl">
                                    <div
                                        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-2xl">
                                        <div
                                            class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                            <svg class="w-8 h-8 text-[#004B7F] ml-1" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                @else
                                <a href="{{ route('news.show', $featuredNews->slug) }}" class="md:w-1/2 h-64 md:h-auto">
                                    <img src="{{ asset($featuredNews->thumbnail ?? $featuredNews->featured_image ?? '/images/news/1.jpg') }}"
                                        alt="{{ $featuredNews->title }}" class="w-full h-full object-cover rounded-2xl">
                                </a>
                                @endif
                                <div class="md:w-1/2 px-6 flex flex-col justify-center">
                                    <div class="mb-3 mt-3 lg:mt-0">
                                        <span
                                            class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-semibold uppercase">
                                            NỔI BẬT
                                        </span>
                                    </div>
                                    <h3
                                        class="text-2xl font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 line-clamp-3">
                                        <a href="{{ route('news.show', $featuredNews->slug) }}">{{ $featuredNews->title
                                            }}</a>
                                    </h3>
                                    <p class="text-secondary text-sm my-4 line-clamp-3 hidden lg:block">
                                        {{ $featuredNews->excerpt }}
                                    </p>
                                    <div class="flex items-center text-xs lg:text-sm text-secondary mt-auto flex-end">
                                        <span>{{ $featuredNews->created_at->format('d/m/Y') }}</span>
                                        <span class="mx-2 text-[#FFAE99] font-bold">•</span>
                                        <span>{{ $featuredNews->created_at->diffForHumans() }}</span>
                                    </div>
                                </div>
                            </article>
                            @endif

                            @foreach($otherNews as $article)
                            <!-- Article {{ $loop->iteration + 1 }} -->
                            <article
                                class="p-3 lg:p-5 group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                @if($article->type == 'video')
                                <div class="relative h-48 cursor-pointer video-trigger"
                                    data-video-url="{{ $article->video_url }}" data-video-title="{{ $article->title }}"
                                    data-video-date="{{ $article->created_at->format('d/m/Y') }}"
                                    data-video-time="{{ $article->created_at->diffForHumans() }}"
                                    data-video-excerpt="{{ $article->excerpt }}">
                                    <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                                        alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                                    <div
                                        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-2xl">
                                        <div
                                            class="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                            <svg class="w-6 h-6 text-[#004B7F] ml-1" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    @if($article->featured)
                                    {{-- <span
                                        class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                        NỔI BẬT
                                    </span> --}}
                                    @endif
                                </div>
                                @else
                                <a href="{{ route('news.show', $article->slug) }}" class="block">
                                    <div class="relative h-48">
                                        <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                                            alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                                        @if($article->featured)
                                        {{-- <span
                                            class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                            NỔI BẬT
                                        </span> --}}
                                        @endif
                                    </div>
                                </a>
                                @endif
                                <div class="py-6">
                                    <div class="flex items-center text-xs lg:text-sm text-secondary mb-3">
                                        <span class="hidden lg:block">{{ $article->created_at->format('d F Y') }}</span>
                                        <span class="lg:hidden">{{ $article->created_at->format('d/m/Y') }}</span>
                                        <span class="text-[#FFAE99] font-bold hidden lg:block">•</span>
                                        <span class="text-[#96B4CA] font-bold mx-[2px] lg:hidden">•</span>
                                        <span class="whitespace-nowrap">{{ $article->created_at->diffForHumans()
                                            }}</span>
                                    </div>
                                    <h3
                                        class="text-lg font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 mb-3 line-clamp-3">
                                        <a href="{{ route('news.show', $article->slug) }}">{{ $article->title }}</a>
                                    </h3>
                                </div>
                            </article>
                            @endforeach
                        </div>
                    </div>
                </div>
            </section>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $otherNews->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Video Modal -->
<div id="videoModal"
    class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden overflow-y-auto p-4">
    <div
        class="bg-white rounded-lg max-w-5xl w-full my-8 relative max-h-[calc(100vh-4rem)] overflow-hidden flex flex-col">
        <!-- Header -->
        <div class="flex justify-between items-center p-4 border-b bg-white flex-shrink-0">
            <h3 id="videoTitle" class="text-lg font-semibold text-gray-900 pr-4"></h3>
            <button onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto">
            <div class="p-6">
                <!-- Video Player -->
                <div class="relative mb-6">
                    <div id="videoPlayer" data-plyr-provider="youtube" data-plyr-embed-id=""></div>
                </div>

                <!-- Video Info -->
                <div id="videoInfo" class="space-y-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <span id="videoDate"></span>
                        <span class="mx-2 text-[#FFAE99] font-bold">•</span>
                        <span id="videoReadTime"></span>
                    </div>
                    <div id="videoExcerpt" class="text-gray-700 leading-relaxed"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let player = null;

function openVideoModal(youtubeLink, title, date, timeAgo, excerpt) {
    const modal = document.getElementById('videoModal');
    const videoPlayer = document.getElementById('videoPlayer');
    const videoTitle = document.getElementById('videoTitle');
    const videoDate = document.getElementById('videoDate');
    const videoReadTime = document.getElementById('videoReadTime');
    const videoExcerpt = document.getElementById('videoExcerpt');

    // Extract video ID from YouTube URL
    let videoId = '';
    if (youtubeLink.includes('youtube.com/watch?v=')) {
        videoId = youtubeLink.split('v=')[1].split('&')[0];
    } else if (youtubeLink.includes('youtu.be/')) {
        videoId = youtubeLink.split('youtu.be/')[1].split('?')[0];
    }

    if (videoId) {
        // Set video info
        videoTitle.textContent = title;
        videoDate.textContent = date;
        videoReadTime.textContent = timeAgo;
        videoExcerpt.textContent = excerpt;

        // Set video ID for Plyr
        videoPlayer.setAttribute('data-plyr-embed-id', videoId);

        // Initialize Plyr
        if (player) {
            player.destroy();
        }
        player = new Plyr('#videoPlayer', {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            youtube: {
                noCookie: false,
                rel: 0,
                showinfo: 0,
                iv_load_policy: 3,
                modestbranding: 1
            }
        });

        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.style.overflow = 'hidden';
    }
}

// Add event listeners for video triggers
document.addEventListener('DOMContentLoaded', function() {
    const videoTriggers = document.querySelectorAll('.video-trigger');
    videoTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const videoUrl = this.getAttribute('data-video-url');
            const videoTitle = this.getAttribute('data-video-title');
            const videoDate = this.getAttribute('data-video-date');
            const videoTime = this.getAttribute('data-video-time');
            const videoExcerpt = this.getAttribute('data-video-excerpt');

            openVideoModal(videoUrl, videoTitle, videoDate, videoTime, videoExcerpt);
        });
    });
});

function closeVideoModal() {
    const modal = document.getElementById('videoModal');

    if (player) {
        player.destroy();
        player = null;
    }

    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('videoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVideoModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeVideoModal();
    }
});
</script>

<!-- Plyr JS -->
<script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>

@endsection