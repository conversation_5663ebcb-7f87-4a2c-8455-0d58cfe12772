@extends('layouts.app')

@section('content')
<div class="bg-backgroundLight">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-5xl mx-auto">
            <!-- Back button & Breadcrumbs -->
            <div class="mb-8">
                <a href="javascript:history.back()"
                    class="w-10 h-10 flex items-center justify-center bg-black text-white rounded-lg mb-4 hover:bg-gray-800 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12H5m5-5l-5 5 5 5">
                        </path>
                    </svg>
                </a>
                <div class="text-sm">
                    <a href="{{ url('/') }}" class="font-medium text-secondary hover:text-primary">Trang chủ</a>
                    <span class="mx-2 text-secondary">></span>
                    <a href="{{ route('news.index') }}" class="font-medium text-secondary hover:text-primary">Tin
                        tức</a>
                    <span class="mx-2 text-secondary">></span>
                    <span class="text-secondary/60">{{ $article->title }}</span>
                </div>
            </div>

            <!-- Article Header -->
            <header class="mb-8">
                <h1 class="text-3xl lg:text-4xl font-bold text-primary mb-4">{{ $article->title }}</h1>
                <div class="flex items-center text-sm text-secondary">
                    <span>{{ $article->created_at->format('d/m/Y') }}</span>
                    <span class="text-[#FFAE99] font-bold mx-[2px]">•</span>
                    <span>{{ $article->created_at->diffForHumans() }}</span>
                </div>
            </header>

            <!-- Header Content -->
            @if(isset($article->header_content) && $article->header_content)
            <div class="mb-8 prose lg:prose-xl max-w-none" id="header-content">
                {!! $article->header_content !!}
            </div>
            @endif

            <!-- Featured Image (only for non-video articles) -->
            @if($article->type != 'video' && isset($article->featured_image) && $article->featured_image)
            <div class="mb-8">
                <img src="{{ asset($article->featured_image) }}" alt="{{ $article->title }}"
                    class="w-full h-auto rounded-2xl shadow-lg">
            </div>
            @endif

            <!-- Main Content Area: 2/3 Content + 1/3 Sidebar -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                <!-- Article Content (2/3) -->
                <div class="lg:col-span-2">
                    <article class="prose lg:prose-xl max-w-none"
                        id="article-content">

                        <!-- Video Player for video articles -->
                        @if($article->type == 'video' && $article->video_url)
                        <div class="not-prose mb-8">
                            <div id="videoPlayer" data-plyr-provider="youtube"
                                data-plyr-embed-id="{{ $article->youtube_video_id }}"
                                class="rounded-2xl overflow-hidden shadow-lg"></div>
                        </div>
                        @endif

                        {!! $article->content !!}
                    </article>
                </div>

                <!-- Sidebar: Recommend for you (1/3) -->
                <div class="lg:col-span-1 hidden lg:block">
                    <h3 class="text-xl font-bold text-primary mb-6">Recommend for you</h3>
                    <div class="sticky top-8">
                        <div class="space-y-6">
                            @foreach($other_news->slice(0, 2) ?? [] as $other)
                            <article class="group rounded-xl bg-white shadow-lg p-3 lg:p-5">
                                @if($other->type == 'video')
                                <div class="relative h-48 mb-3 cursor-pointer video-trigger"
                                    data-video-url="{{ $other->video_url }}" data-video-title="{{ $other->title }}"
                                    data-video-date="{{ $other->created_at->format('d/m/Y') }}"
                                    data-video-time="{{ $other->created_at->diffForHumans() }}"
                                    data-video-excerpt="{{ $other->excerpt }}">
                                    <img src="{{ asset($other->thumbnail ?? $other->featured_image ?? '/images/news/1.jpg') }}"
                                        alt="{{ $other->title }}" class="w-full h-full object-cover rounded-lg">
                                    <div
                                        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
                                        <div
                                            class="w-8 h-8 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                            <svg class="w-4 h-4 text-[#004B7F] ml-0.5" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z" />
                                            </svg>
                                        </div>
                                    </div>
                                    @if($other->featured)
                                    {{-- <span
                                        class="absolute top-2 left-2 px-2 py-1 rounded text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                        NỔI BẬT
                                    </span> --}}
                                    @endif
                                </div>
                                <div class="flex items-center text-xs text-secondary mb-2">
                                    <span>{{ $other->created_at->format('d/m/Y') }}</span>
                                    <span class="text-[#FFAE99] font-bold mx-[2px]">•</span>
                                    <span>{{ $other->created_at->diffForHumans() }}</span>
                                </div>
                                <a href="{{ route('news.show', $other->slug) }}" class="block">
                                    <h4
                                        class="text-sm font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 line-clamp-3">
                                        {{ $other->title }}
                                    </h4>
                                </a>
                                @else
                                <a href="{{ route('news.show', $other->slug) }}" class="block">
                                    <div class="relative h-48 mb-3">
                                        <img src="{{ asset($other->thumbnail ?? $other->featured_image ?? '/images/news/1.jpg') }}"
                                            alt="{{ $other->title }}" class="w-full h-full object-cover rounded-lg">
                                        @if($other->featured)
                                        {{-- <span
                                            class="absolute top-2 left-2 px-2 py-1 rounded text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                            NỔI BẬT
                                        </span> --}}
                                        @endif
                                    </div>
                                    <div class="flex items-center text-xs text-secondary mb-2">
                                        <span>{{ $other->created_at->format('d/m/Y') }}</span>
                                        <span class="text-[#FFAE99] font-bold mx-[2px]">•</span>
                                        <span>{{ $other->created_at->diffForHumans() }}</span>
                                    </div>
                                    <h4
                                        class="text-sm font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 line-clamp-3">
                                        {{ $other->title }}
                                    </h4>
                                </a>
                                @endif
                            </article>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Form Section -->
@include('components.appointment-form')

<section class="py-16 bg-backgroundLight">
    <div class="container mx-auto px-4">
        <div class="max-w-5xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-primary mb-4">Tin Tức Khác</h2>
            </div>

            <!-- News Grid -->
            <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 lg:gap-7 gap-3 mb-12">
                @foreach($other_news as $index => $article)
                <!-- Article {{ $loop->iteration + 1 }} -->
                <article
                    class="@if($index == 0) lg:hidden @endif p-3 lg:p-5 group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    @if($article->type == 'video')
                    <div class="relative h-48 cursor-pointer video-trigger" data-video-url="{{ $article->video_url }}"
                        data-video-title="{{ $article->title }}"
                        data-video-date="{{ $article->created_at->format('d/m/Y') }}"
                        data-video-time="{{ $article->created_at->diffForHumans() }}"
                        data-video-excerpt="{{ $article->excerpt }}">
                        <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                            alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                        <div
                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-2xl">
                            <div class="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-[#004B7F] ml-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </div>
                        </div>
                        @if($article->featured)
                        {{-- <span
                            class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                            NỔI BẬT
                        </span> --}}
                        @endif
                    </div>
                    @else
                    <a href="{{ route('news.show', $article->slug) }}" class="block">
                        <div class="relative h-48">
                            <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                                alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                            @if($article->featured)
                            {{-- <span
                                class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                NỔI BẬT
                            </span> --}}
                            @endif
                        </div>
                    </a>
                    @endif
                    <div class="py-6">
                        <div class="flex items-center text-xs lg:text-sm text-secondary mb-3">
                            <span class="hidden lg:block">{{ $article->created_at->format('d F Y') }}</span>
                            <span class="lg:hidden">{{ $article->created_at->format('d/m/Y') }}</span>
                            <span class="text-[#FFAE99] font-bold hidden lg:block mx-[2px]">•</span>
                            <span class="text-[#96B4CA] font-bold mx-[2px] lg:hidden">•</span>
                            <span class="whitespace-nowrap">{{ $article->created_at->diffForHumans()
                                }}</span>
                        </div>
                        <h3
                            class="text-lg font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 mb-3 line-clamp-3">
                            <a href="{{ route('news.show', $article->slug) }}">{{ $article->title }}</a>
                        </h3>
                    </div>
                </article>
                @endforeach
            </div>
        </div>
    </div>
</section>

<!-- Video Modal -->
<div id="videoModal"
    class="fixed inset-0 bg-black bg-opacity-75 items-center justify-center z-50 hidden overflow-y-auto p-4">
    <div
        class="bg-white rounded-lg max-w-5xl w-full my-8 relative max-h-[calc(100vh-4rem)] overflow-hidden flex flex-col">
        <!-- Header -->
        <div class="flex justify-between items-center p-4 border-b bg-white flex-shrink-0">
            <h3 id="videoTitle" class="text-lg font-semibold text-gray-900 pr-4"></h3>
            <button onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto">
            <div class="p-6">
                <!-- Video Player -->
                <div class="relative mb-6">
                    <div id="videoPlayerModal" data-plyr-provider="youtube" data-plyr-embed-id=""></div>
                </div>

                <!-- Video Info -->
                <div id="videoInfo" class="space-y-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <span id="videoDate"></span>
                        <span class="text-[#FFAE99] font-bold mx-[2px]">•</span>
                        <span id="videoReadTime"></span>
                    </div>
                    <div id="videoExcerpt" class="text-gray-700 leading-relaxed"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    /* Ensure oembed elements are hidden until processed */
    oembed {
        display: none;
    }

    /* YouTube embed responsive styling */
    .youtube-embed-wrapper {
        position: relative;
        padding-bottom: 56.25%;
        /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        margin: 1.5rem 0;
    }

    .youtube-embed-wrapper iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>

<script>
    let player = null;
let modalPlayer = null;

function openVideoModal(youtubeLink, title, date, timeAgo, excerpt) {
    console.log('Opening video modal with:', { youtubeLink, title, date, timeAgo, excerpt });

    const modal = document.getElementById('videoModal');
    const videoPlayerModal = document.getElementById('videoPlayerModal');
    const videoTitle = document.getElementById('videoTitle');
    const videoDate = document.getElementById('videoDate');
    const videoReadTime = document.getElementById('videoReadTime');
    const videoExcerpt = document.getElementById('videoExcerpt');

    console.log('Modal elements:', { modal, videoPlayerModal, videoTitle, videoDate, videoReadTime, videoExcerpt });

    // Extract video ID from YouTube URL
    let videoId = '';
    if (youtubeLink.includes('youtube.com/watch?v=')) {
        videoId = youtubeLink.split('v=')[1].split('&')[0];
    } else if (youtubeLink.includes('youtu.be/')) {
        videoId = youtubeLink.split('youtu.be/')[1].split('?')[0];
    }

    console.log('Extracted video ID:', videoId);

    if (videoId) {
        // Set video info
        videoTitle.textContent = title;
        videoDate.textContent = date;
        videoReadTime.textContent = timeAgo;
        videoExcerpt.textContent = excerpt;

        // Set video ID for Plyr
        videoPlayerModal.setAttribute('data-plyr-embed-id', videoId);

        // Initialize Plyr
        if (modalPlayer) {
            modalPlayer.destroy();
        }
        modalPlayer = new Plyr('#videoPlayerModal', {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            youtube: {
                noCookie: false,
                rel: 0,
                showinfo: 0,
                iv_load_policy: 3,
                modestbranding: 1
            }
        });

        console.log('Modal player initialized:', modalPlayer);

        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.style.overflow = 'hidden';

        console.log('Modal opened');
    } else {
        console.error('No video ID extracted from URL:', youtubeLink);
    }
}

function closeVideoModal() {
    const modal = document.getElementById('videoModal');

    if (modalPlayer) {
        modalPlayer.destroy();
        modalPlayer = null;
    }

    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('videoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVideoModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeVideoModal();
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Initialize main video player if exists
    const mainVideoPlayer = document.getElementById('videoPlayer');
    console.log('Main video player element:', mainVideoPlayer);

    if (mainVideoPlayer) {
        const videoId = mainVideoPlayer.getAttribute('data-plyr-embed-id');
        console.log('Video ID:', videoId);

        if (videoId) {
            player = new Plyr('#videoPlayer', {
                controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
                youtube: {
                    noCookie: false,
                    rel: 0,
                    showinfo: 0,
                    iv_load_policy: 3,
                    modestbranding: 1
                }
            });
            console.log('Main video player initialized:', player);
        } else {
            console.log('No video ID found for main player');
        }
    } else {
        console.log('No main video player element found');
    }

    // Add event listeners for video triggers
    const videoTriggers = document.querySelectorAll('.video-trigger');
    videoTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const videoUrl = this.getAttribute('data-video-url');
            const videoTitle = this.getAttribute('data-video-title');
            const videoDate = this.getAttribute('data-video-date');
            const videoTime = this.getAttribute('data-video-time');
            const videoExcerpt = this.getAttribute('data-video-excerpt');

            openVideoModal(videoUrl, videoTitle, videoDate, videoTime, videoExcerpt);
        });
    });

    // Process oembed tags for YouTube videos
    const oembedElements = document.querySelectorAll('oembed[url]');

    oembedElements.forEach(function(oembed) {
        const url = oembed.getAttribute('url');

        if (url && (url.includes('youtube.com') || url.includes('youtu.be'))) {
            // Extract video ID from YouTube URL
            let videoId = '';

            if (url.includes('youtube.com/watch?v=')) {
                videoId = url.split('v=')[1].split('&')[0];
            } else if (url.includes('youtu.be/')) {
                videoId = url.split('youtu.be/')[1].split('?')[0];
            }

            if (videoId) {
                // Create YouTube embed iframe
                const iframe = document.createElement('iframe');
                iframe.src = `https://www.youtube.com/embed/${videoId}`;
                iframe.width = '100%';
                iframe.height = '400';
                iframe.frameBorder = '0';
                iframe.allowFullscreen = true;
                iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                iframe.className = 'rounded-lg shadow-lg';

                // Create wrapper div
                const wrapper = document.createElement('div');
                wrapper.className = 'youtube-embed-wrapper';

                wrapper.appendChild(iframe);

                // Replace the oembed element with the iframe
                oembed.parentNode.replaceChild(wrapper, oembed);
            }
        }
    });

    // Also process header content if it exists
    const headerContent = document.querySelector('#header-content');
    if (headerContent) {
        const headerOembeds = headerContent.querySelectorAll('oembed[url]');
        headerOembeds.forEach(function(oembed) {
            const url = oembed.getAttribute('url');

            if (url && (url.includes('youtube.com') || url.includes('youtu.be'))) {
                let videoId = '';

                if (url.includes('youtube.com/watch?v=')) {
                    videoId = url.split('v=')[1].split('&')[0];
                } else if (url.includes('youtu.be/')) {
                    videoId = url.split('youtu.be/')[1].split('?')[0];
                }

                if (videoId) {
                    const iframe = document.createElement('iframe');
                    iframe.src = `https://www.youtube.com/embed/${videoId}`;
                    iframe.width = '100%';
                    iframe.height = '400';
                    iframe.frameBorder = '0';
                    iframe.allowFullscreen = true;
                    iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
                    iframe.className = 'rounded-lg shadow-lg';

                    const wrapper = document.createElement('div');
                    wrapper.className = 'youtube-embed-wrapper';

                    wrapper.appendChild(iframe);
                    oembed.parentNode.replaceChild(wrapper, oembed);
                }
            }
        });
    }
});
</script>

@endsection