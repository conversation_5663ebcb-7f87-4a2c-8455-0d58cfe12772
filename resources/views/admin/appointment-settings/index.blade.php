@extends('admin.layouts.app')

@section('content')
<style>
    .time-slot-checkbox:checked+.custom-control-label::before {
        background-color: #007bff;
        border-color: #007bff;
    }

    .custom-control-label {
        font-weight: 500;
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        background-color: #f8f9fa;
        transition: all 0.2s ease;
        cursor: pointer;
        display: block;
        text-align: center;
        margin-bottom: 0;
    }

    .time-slot-checkbox:checked+.custom-control-label {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .custom-control-label:hover {
        background-color: #e9ecef;
    }

    .time-slot-checkbox:checked+.custom-control-label:hover {
        background-color: #0056b3;
    }

    .custom-control-input {
        position: absolute;
        left: -9999px;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Cài Đặt Lịch Hẹn</h3>
                </div>

                <form action="{{ route('admin.appointment-settings.update') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="card-body">
                        @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                        @endif

                        <!-- Time Slots Section -->
                        <div class="form-group">
                            <label for="time_slots">Khung Giờ Khám</label>
                            <p class="text-muted mb-3">Chọn các khung giờ mà bệnh nhân có thể đặt lịch hẹn (từ 7:00 sáng
                                đến 22:00 tối)</p>

                            <!-- Time Slots Grid -->
                            <div class="row" id="time-slots-grid">
                                @php
                                $selectedSlots = is_array($timeSlots) ? $timeSlots : [];
                                $startHour = 7;
                                $endHour = 22;
                                @endphp

                                @for($hour = $startHour; $hour <= $endHour; $hour++) @foreach(['00', '30' ] as $minute)
                                    @php $timeValue=sprintf('%02d:%s', $hour, $minute); $isSelected=in_array($timeValue,
                                    $selectedSlots); @endphp <div class="col-md-2 col-sm-3 col-4 mb-2">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input time-slot-checkbox"
                                            id="time_{{ $timeValue }}" name="time_slots[]" value="{{ $timeValue }}" {{
                                            $isSelected ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="time_{{ $timeValue }}">
                                            {{ $timeValue }}
                                        </label>
                                    </div>
                            </div>
                            @endforeach
                            @endfor
                        </div>

                        <!-- Quick Select Buttons -->
                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="select-morning">
                                <i class="fas fa-sun"></i> Chọn Buổi Sáng (7:00-12:00)
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="select-afternoon">
                                <i class="fas fa-cloud-sun"></i> Chọn Buổi Chiều (13:00-17:00)
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="select-evening">
                                <i class="fas fa-moon"></i> Chọn Buổi Tối (18:00-22:00)
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="select-all">
                                <i class="fas fa-check-double"></i> Chọn Tất Cả
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="clear-all">
                                <i class="fas fa-times"></i> Bỏ Chọn Tất Cả
                            </button>
                        </div>

                        <small class="form-text text-muted mt-2">
                            Chọn các khung giờ mà bệnh nhân có thể đặt lịch hẹn. Mỗi khung giờ cách nhau 30 phút.
                        </small>
                    </div>

                    <!-- Max Appointments Per Slot -->
                    <div class="form-group">
                        <label for="max_per_slot">Số Lượng Tối Đa Mỗi Khung Giờ</label>
                        <input type="number" name="max_per_slot" id="max_per_slot" class="form-control"
                            value="{{ $maxPerSlot }}" min="1" max="10" required>
                        <small class="form-text text-muted">
                            Số lượng cuộc hẹn tối đa cho mỗi khung giờ (1-10).
                        </small>
                    </div>

                    <!-- Days Ahead -->
                    <div class="form-group">
                        <label for="days_ahead">Số Ngày Cho Phép Đặt Trước</label>
                        <input type="number" name="days_ahead" id="days_ahead" class="form-control"
                            value="{{ $daysAhead }}" min="1" max="30" required>
                        <small class="form-text text-muted">
                            Số ngày tối đa mà bệnh nhân có thể đặt lịch trước (1-30 ngày).
                        </small>
                    </div>
            </div>

            <div class="card-footer">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Lưu Cài Đặt
                </button>
            </div>
            </form>
        </div>
    </div>
</div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Quick select buttons
    document.getElementById('select-morning').addEventListener('click', function() {
        selectTimeRange('07:00', '12:00');
    });

    document.getElementById('select-afternoon').addEventListener('click', function() {
        selectTimeRange('13:00', '17:00');
    });

    document.getElementById('select-evening').addEventListener('click', function() {
        selectTimeRange('18:00', '22:00');
    });

    document.getElementById('select-all').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.time-slot-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    });

    document.getElementById('clear-all').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.time-slot-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    });

    function selectTimeRange(startTime, endTime) {
        const checkboxes = document.querySelectorAll('.time-slot-checkbox');
        checkboxes.forEach(checkbox => {
            const timeValue = checkbox.value;
            if (timeValue >= startTime && timeValue <= endTime) {
                checkbox.checked = true;
            }
        });
    }

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('.time-slot-checkbox:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Vui lòng chọn ít nhất một khung giờ!');
        }
    });
});
</script>
@endsection