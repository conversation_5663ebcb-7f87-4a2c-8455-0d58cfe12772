@extends('admin.layouts.app')

@section('title', 'Chi tiết câu hỏi')
@section('header', 'Chi tiết câu hỏi')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Câu hỏi #{{ $question->id }}</h3>
        <div class="flex space-x-2">
            <a href="{{ route('admin.questions') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-1"></i> Quay lại
            </a>
            <a href="{{ route('admin.questions.edit', $question->id) }}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-edit mr-1"></i> Chỉnh sửa
            </a>
            <form action="{{ route('admin.questions.delete', $question->id) }}" method="POST" class="inline-block">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" onclick="return confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')">
                    <i class="fas fa-trash mr-1"></i> Xóa
                </button>
            </form>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Thông tin người hỏi</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Họ và tên</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $question->name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Email</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $question->email }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Số điện thoại</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $question->phone ?? 'Không có' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Thông tin câu hỏi</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Ngày tạo</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $question->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Trạng thái</p>
                            <p class="mt-1">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {{ $question->status == 'answered' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $question->status == 'answered' ? 'Đã trả lời' : 'Chưa trả lời' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Nội dung câu hỏi</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-900">{{ $question->question }}</p>
            </div>
        </div>
        
        @if($question->status == 'answered')
        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Câu trả lời</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-900">{{ $question->answer }}</p>
                <p class="mt-2 text-xs text-gray-500">Trả lời bởi: {{ $question->answered_by }} - {{ $question->answered_at->format('d/m/Y H:i') }}</p>
            </div>
        </div>
        @else
        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Trả lời câu hỏi</h4>
            <form action="{{ route('admin.questions.answer', $question->id) }}" method="POST">
                @csrf
                <div class="bg-gray-50 p-4 rounded-lg">
                    <textarea name="answer" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Nhập câu trả lời..." required></textarea>
                    <div class="mt-4 flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-paper-plane mr-1"></i> Gửi câu trả lời
                        </button>
                    </div>
                </div>
            </form>
        </div>
        @endif
    </div>
</div>
@endsection