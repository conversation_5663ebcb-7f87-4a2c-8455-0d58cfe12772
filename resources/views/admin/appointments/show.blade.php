@extends('admin.layouts.app')

@section('title', 'Chi tiết lịch hẹn')
@section('header', 'Chi tiết lịch hẹn')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Thông tin lịch hẹn #{{ $appointment->id }}</h3>
        <div class="flex space-x-2">
            <a href="{{ route('admin.appointments') }}"
                class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                <i class="fas fa-arrow-left mr-1"></i> Quay lại
            </a>
            @if($appointment->status == 'pending' || $appointment->status == 'cancelled')
            <a href="{{ route('admin.appointments.confirm', $appointment->id) }}"
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                onclick="return confirm('{{ $appointment->status == 'cancelled' ? 'Xác nhận lại lịch hẹn này?' : 'Xác nhận lịch hẹn này?' }}')">
                <i class="fas fa-check mr-1"></i> {{ $appointment->status == 'cancelled' ? 'Xác nhận lại' : 'Xác nhận'
                }}
            </a>
            @endif
            @if($appointment->status != 'cancelled')
            <a href="{{ route('admin.appointments.cancel', $appointment->id) }}"
                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                onclick="return confirm('Hủy lịch hẹn này?')">
                <i class="fas fa-times mr-1"></i> Hủy
            </a>
            @endif
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Thông tin cá nhân</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Họ và tên</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $appointment->name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Năm sinh</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $appointment->birth_year }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Số điện thoại</p>
                            <p class="mt-1 text-sm text-gray-900">{{ $appointment->phone }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Thông tin lịch hẹn</h4>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Ngày hẹn</p>
                            <p class="mt-1 text-sm text-gray-900">
                                @if($appointment->appointment_date)
                                {{ $appointment->appointment_date->format('d/m/Y') }}
                                <span class="text-gray-500">({{ $appointment->appointment_date->locale('vi')->dayName
                                    }})</span>
                                @else
                                <span class="text-gray-400">Chưa có</span>
                                @endif
                            </p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Giờ hẹn</p>
                            <p class="mt-1 text-sm text-gray-900">
                                @if($appointment->appointment_time)
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                    {{ $appointment->appointment_time }}
                                </span>
                                @else
                                <span class="text-gray-400">Chưa có</span>
                                @endif
                            </p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Loại bệnh nhân</p>
                            <p class="mt-1">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $appointment->status_type == 'new' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                                    {{ $appointment->status_type == 'new' ? 'Bệnh nhân mới' : 'Tái khám' }}
                                </span>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Trạng thái</p>
                            <p class="mt-1">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {{ $appointment->status == 'confirmed' ? 'bg-green-100 text-green-800' : 
                                       ($appointment->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ $appointment->status == 'confirmed' ? 'Đã xác nhận' :
                                    ($appointment->status == 'pending' ? 'Chờ xác nhận' : 'Đã hủy') }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Ghi chú</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-900">{{ $appointment->notes ?: 'Không có ghi chú' }}</p>
            </div>
        </div>

        <div class="mt-6">
            <h4 class="text-sm font-medium text-gray-500 uppercase mb-2">Lịch sử</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flow-root">
                    <ul class="-mb-8">
                        <li>
                            <div class="relative pb-8">
                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                                    aria-hidden="true"></span>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span
                                            class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <i class="fas fa-plus text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Lịch hẹn được tạo</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ $appointment->created_at->format('d/m/Y H:i') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>

                        @if($appointment->status != 'pending')
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span
                                            class="h-8 w-8 rounded-full {{ $appointment->status == 'confirmed' ? 'bg-green-500' : 'bg-red-500' }} flex items-center justify-center ring-8 ring-white">
                                            <i
                                                class="fas {{ $appointment->status == 'confirmed' ? 'fa-check' : 'fa-times' }} text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Lịch hẹn đã {{ $appointment->status ==
                                                'confirmed' ? 'được xác nhận' : 'bị hủy' }}</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ $appointment->updated_at->format('d/m/Y H:i') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection