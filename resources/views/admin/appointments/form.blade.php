@extends('admin.layouts.app')

@section('title', isset($appointment) ? 'Chỉnh sửa lịch hẹn' : 'Tạo lịch hẹn mới')
@section('header', isset($appointment) ? 'Chỉnh sửa lịch hẹn' : 'Tạo lịch hẹn mới')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
            {{ isset($appointment) ? 'Chỉnh sửa lịch hẹn' : 'Tạo lịch hẹn mới' }}
        </h3>
    </div>
    
    <form method="POST" action="{{ isset($appointment) ? route('admin.appointments.update', $appointment->id) : route('admin.appointments.store') }}" class="p-6">
        @csrf
        @if(isset($appointment))
            @method('PUT')
        @endif
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    H<PERSON> và tên <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name', $appointment->name ?? '') }}"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('name') border-red-500 @enderror">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email <span class="text-red-500">*</span>
                </label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       value="{{ old('email', $appointment->email ?? '') }}"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('email') border-red-500 @enderror">
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                    Số điện thoại <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="phone" 
                       name="phone" 
                       value="{{ old('phone', $appointment->phone ?? '') }}"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('phone') border-red-500 @enderror">
                @error('phone')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="birth_year" class="block text-sm font-medium text-gray-700 mb-2">
                    Năm sinh
                </label>
                <input type="number" 
                       id="birth_year" 
                       name="birth_year" 
                       value="{{ old('birth_year', $appointment->birth_year ?? '') }}"
                       min="1900" 
                       max="{{ date('Y') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('birth_year') border-red-500 @enderror">
                @error('birth_year')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                    Ngày hẹn <span class="text-red-500">*</span>
                </label>
                <input type="date" 
                       id="date" 
                       name="date" 
                       value="{{ old('date', $appointment->date ?? '') }}"
                       required
                       min="{{ date('Y-m-d') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('date') border-red-500 @enderror">
                @error('date')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="time" class="block text-sm font-medium text-gray-700 mb-2">
                    Giờ hẹn <span class="text-red-500">*</span>
                </label>
                <select id="time" 
                        name="time" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('time') border-red-500 @enderror">
                    <option value="">Chọn giờ</option>
                    @foreach(['08:00', '09:00', '10:00', '11:00', '14:00', '15:00', '16:00'] as $timeSlot)
                        <option value="{{ $timeSlot }}" {{ old('time', $appointment->time ?? '') == $timeSlot ? 'selected' : '' }}>
                            {{ $timeSlot }}
                        </option>
                    @endforeach
                </select>
                @error('time')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="md:col-span-2">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái <span class="text-red-500">*</span>
                </label>
                <select id="status" 
                        name="status" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('status') border-red-500 @enderror">
                    <option value="pending" {{ old('status', $appointment->status ?? '') == 'pending' ? 'selected' : '' }}>
                        Chờ xác nhận
                    </option>
                    <option value="confirmed" {{ old('status', $appointment->status ?? '') == 'confirmed' ? 'selected' : '' }}>
                        Đã xác nhận
                    </option>
                    <option value="cancelled" {{ old('status', $appointment->status ?? '') == 'cancelled' ? 'selected' : '' }}>
                        Đã hủy
                    </option>
                </select>
                @error('status')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="md:col-span-2">
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                    Tin nhắn
                </label>
                <textarea id="message" 
                          name="message" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('message') border-red-500 @enderror">{{ old('message', $appointment->message ?? '') }}</textarea>
                @error('message')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            @if(isset($appointment))
            <div class="md:col-span-2">
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Ghi chú của admin
                </label>
                <textarea id="notes" 
                          name="notes" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('notes') border-red-500 @enderror">{{ old('notes', $appointment->notes ?? '') }}</textarea>
                @error('notes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            @endif
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
            <a href="{{ route('admin.appointments') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Hủy
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                {{ isset($appointment) ? 'Cập nhật' : 'Tạo mới' }}
            </button>
        </div>
    </form>
</div>
@endsection
