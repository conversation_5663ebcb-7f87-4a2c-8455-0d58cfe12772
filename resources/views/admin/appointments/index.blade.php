@extends('admin.layouts.app')

@section('title', 'Quản lý lịch hẹn')
@section('header', 'Quản lý lịch hẹn')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900"><PERSON><PERSON> sách lịch hẹn</h3>
        <div class="flex space-x-2">
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                    class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-filter mr-1"></i> Lọc
                </button>
                <div x-show="open" @click.away="open = false"
                    class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100"
                    style="display: none;">
                    <div class="py-1">
                        <a href="{{ route('admin.appointments', ['status' => 'all']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tất cả</a>
                        <a href="{{ route('admin.appointments', ['status' => 'pending']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Chờ xác nhận</a>
                        <a href="{{ route('admin.appointments', ['status' => 'confirmed']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đã xác nhận</a>
                        <a href="{{ route('admin.appointments', ['status' => 'cancelled']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đã hủy</a>
                    </div>
                </div>
            </div>
            <form method="GET" action="{{ route('admin.appointments') }}" class="flex">
                <input type="text" name="search" placeholder="Tìm kiếm..." value="{{ request('search') }}"
                    class="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-primary focus:border-primary">
                <button type="submit"
                    class="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Năm
                            sinh</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số
                            điện thoại</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giờ
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng
                            thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao
                            tác</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($appointments as $appointment)
                    <tr>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $appointment->id }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $appointment->name }}</div>
                            @if($appointment->notes)
                            <div class="text-sm text-gray-500" title="{{ $appointment->notes }}">
                                {{ Str::limit($appointment->notes, 30) }}
                            </div>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $appointment->birth_year }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $appointment->phone }}</div>
                            <div class="text-sm text-gray-500">{{ $appointment->created_at->format('d/m/Y H:i') }}</div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($appointment->appointment_date)
                            <div class="text-sm font-medium text-gray-900">{{
                                $appointment->appointment_date->format('d/m/Y') }}</div>
                            <div class="text-sm text-gray-500">{{ $appointment->appointment_date->locale('vi')->dayName
                                }}</div>
                            @else
                            <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($appointment->appointment_time)
                            <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                {{ $appointment->appointment_time }}
                            </span>
                            @else
                            <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $appointment->status_type == 'new' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                                {{ $appointment->status_type == 'new' ? 'Bệnh nhân mới' : 'Tái khám' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $appointment->status == 'confirmed' ? 'bg-green-100 text-green-800' : 
                                   ($appointment->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ $appointment->status == 'confirmed' ? 'Đã xác nhận' :
                                ($appointment->status == 'pending' ? 'Chờ xác nhận' : 'Đã hủy') }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                            <a href="{{ route('admin.appointments.show', $appointment->id) }}"
                                class="text-primary hover:text-primary-dark mr-3">
                                <i class="fas fa-eye"></i>
                            </a>
                            @if($appointment->status == 'pending' || $appointment->status == 'cancelled')
                            <a href="{{ route('admin.appointments.confirm', $appointment->id) }}"
                                class="text-green-600 hover:text-green-900 mr-3"
                                onclick="return confirm('{{ $appointment->status == 'cancelled' ? 'Xác nhận lại lịch hẹn này?' : 'Xác nhận lịch hẹn này?' }}')"
                                title="{{ $appointment->status == 'cancelled' ? 'Xác nhận lại' : 'Xác nhận' }}">
                                <i class="fas fa-check"></i>
                            </a>
                            @endif
                            @if($appointment->status != 'cancelled')
                            <a href="{{ route('admin.appointments.cancel', $appointment->id) }}"
                                class="text-red-600 hover:text-red-900" onclick="return confirm('Hủy lịch hẹn này?')">
                                <i class="fas fa-times"></i>
                            </a>
                            @endif
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="px-4 py-3 text-center text-gray-500">Không có lịch hẹn nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $appointments->links() }}
        </div>
    </div>
</div>
@endsection