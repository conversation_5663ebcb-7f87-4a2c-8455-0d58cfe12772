@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('header', 'Dashboard')

@section('content')
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-500">
                <i class="fas fa-calendar-alt text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Lịch hẹn hôm nay</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $todayAppointments }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-500">
                <i class="fas fa-calendar-check text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Tổng lịch hẹn</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $totalAppointments }}</p>
            </div>
        </div>
    </div>

    {{-- <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-500">
                <i class="fas fa-question-circle text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Câu hỏi mới</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $newQuestions }}</p>
            </div>
        </div>
    </div> --}}

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-500">
                <i class="fas fa-newspaper text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Bài viết</p>
                <p class="text-2xl font-semibold text-gray-900">{{ $totalNews }}</p>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Appointments -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Lịch hẹn gần đây</h3>
        </div>
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tên</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ngày</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Giờ</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($recentAppointments as $appointment)
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap">{{ $appointment->name }}</td>
                            <td class="px-4 py-3 whitespace-nowrap">{{ $appointment->appointment_date->format('d/m/Y')
                                }}</td>
                            <td class="px-4 py-3 whitespace-nowrap">{{ $appointment->appointment_time }}</td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {{ $appointment->status == 'confirmed' ? 'bg-green-100 text-green-800' : 
                                       ($appointment->status == 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ $appointment->status == 'confirmed' ? 'Đã xác nhận' :
                                    ($appointment->status == 'pending' ? 'Chờ xác nhận' : 'Đã hủy') }}
                                </span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="px-4 py-3 text-center text-gray-500">Không có lịch hẹn nào</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-right">
                <a href="{{ route('admin.appointments') }}"
                    class="text-sm font-medium text-primary hover:text-primary-dark">
                    Xem tất cả <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Questions -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Câu hỏi gần đây</h3>
        </div>
        <div class="p-4">
            <div class="space-y-4">
                @forelse($recentQuestions as $question)
                <div class="border-b border-gray-200 pb-4 last:border-0 last:pb-0">
                    <h4 class="text-sm font-medium text-gray-900">{{ $question->name }} <span
                            class="text-gray-500 text-xs">{{ $question->created_at->diffForHumans() }}</span></h4>
                    <p class="text-sm text-gray-600 mt-1">{{ Str::limit($question->question, 100) }}</p>
                    <div class="mt-2">
                        <span
                            class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full 
                            {{ $question->status == 'answered' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ $question->status == 'answered' ? 'Đã trả lời' : 'Chưa trả lời' }}
                        </span>
                    </div>
                </div>
                @empty
                <div class="text-center text-gray-500">Không có câu hỏi nào</div>
                @endforelse
            </div>
            <div class="mt-4 text-right">
                <a href="{{ route('admin.questions') }}"
                    class="text-sm font-medium text-primary hover:text-primary-dark">
                    Xem tất cả <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection