@extends('admin.layouts.app')

@section('title', isset($news) ? 'Chỉnh sửa tin tức' : 'Thêm tin tức mới')
@section('header', isset($news) ? 'Chỉnh sửa tin tức' : 'Thêm tin tức mới')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ isset($news) ? 'Chỉnh sửa tin tức' : 'Thêm tin tức mới' }}</h3>
    </div>
    <form action="{{ isset($news) ? route('admin.news.update', $news->id) : route('admin.news.store') }}" method="POST"
        enctype="multipart/form-data">
        @csrf
        @if(isset($news))
        @method('PUT')
        @endif

        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Ti<PERSON><PERSON> đ<PERSON> <span
                            class="text-red-500">*</span></label>
                    <input type="text" name="title" id="title"
                        value="{{ old('title', isset($news) ? $news->title : '') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                        required>
                    @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                    <input type="text" name="slug" id="slug" value="{{ old('slug', isset($news) ? $news->slug : '') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    <p class="mt-1 text-xs text-gray-500">Để trống để tự động tạo từ tiêu đề</p>
                    @error('slug')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Danh mục <span
                            class="text-red-500">*</span></label>
                    <select name="category" id="category"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                        required>
                        <option value="">Chọn danh mục</option>
                        @foreach($categories as $cat)
                        <option value="{{ $cat->slug }}" {{ old('category', isset($news) ? $news->category : '') ==
                            $cat->slug ? 'selected' : '' }}>
                            {{ $cat->name }}
                        </option>
                        @endforeach
                    </select>
                    @error('category')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Loại tin tức <span
                            class="text-red-500">*</span></label>
                    <select name="type" id="type"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                        required>
                        <option value="post" {{ old('type', isset($news) ? $news->type : 'post') == 'post' ?
                            'selected' : '' }}>Bài viết</option>
                        <option value="video" {{ old('type', isset($news) ? $news->type : '') == 'video' ?
                            'selected' : '' }}>Video</option>
                    </select>
                    @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Trạng thái <span
                            class="text-red-500">*</span></label>
                    <select name="status" id="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                        required>
                        <option value="draft" {{ old('status', isset($news) ? $news->status : 'draft') == 'draft' ?
                            'selected' : '' }}>Bản nháp</option>
                        <option value="published" {{ old('status', isset($news) ? $news->status : '') == 'published' ?
                            'selected' : '' }}>Đã xuất bản</option>
                    </select>
                    @error('status')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- YouTube Link (for video type) -->
            <div id="youtube-link-section" style="display: none;">
                <label for="youtube_link" class="block text-sm font-medium text-gray-700 mb-1">Link YouTube <span
                        class="text-red-500">*</span></label>
                <input type="url" name="youtube_link" id="youtube_link"
                    value="{{ old('youtube_link', isset($news) ? $news->video_url : '') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    placeholder="https://www.youtube.com/watch?v=...">
                <p class="mt-1 text-xs text-gray-500">Nhập link YouTube để hiển thị video</p>
                @error('youtube_link')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Featured Checkbox -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" name="featured" id="featured" value="1" {{ old('featured', isset($news) ?
                        $news->featured : false) ? 'checked' : '' }}
                    class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="featured" class="ml-2 block text-sm text-gray-900">
                        Tin tức nổi bật
                    </label>
                </div>
                <p class="mt-1 text-xs text-gray-500">Chỉ 1 tin tức nổi bật sẽ hiển thị trên trang chủ, các tin khác sẽ
                    hiển thị bình thường</p>
                @error('featured')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Images Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-1">Hình ảnh thumbnail (hiển
                        thị trong danh sách)</label>
                    <input type="file" name="thumbnail" id="thumbnail" accept="image/*"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    <p class="mt-1 text-xs text-gray-500">Kích thước tối đa: 5MB. Định dạng: JPG, PNG, GIF</p>
                    @if(isset($news) && $news->thumbnail)
                    <div class="mt-2">
                        <img src="{{ asset($news->thumbnail) }}" alt="Current thumbnail"
                            class="h-20 w-20 object-cover rounded-md">
                        <p class="text-xs text-gray-500 mt-1">Hình ảnh hiện tại</p>
                    </div>
                    @endif
                    @error('thumbnail')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-1">Hình ảnh chính
                        (hiển thị dưới header)</label>
                    <input type="file" name="featured_image" id="featured_image" accept="image/*"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    <p class="mt-1 text-xs text-gray-500">Kích thước tối đa: 5MB. Định dạng: JPG, PNG, GIF</p>
                    @if(isset($news) && $news->featured_image)
                    <div class="mt-2">
                        <img src="{{ asset($news->featured_image) }}" alt="Current featured image"
                            class="h-20 w-32 object-cover rounded-md">
                        <p class="text-xs text-gray-500 mt-1">Hình ảnh hiện tại</p>
                    </div>
                    @endif
                    @error('featured_image')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Header Content -->
            <div>
                <label for="header_content" class="block text-sm font-medium text-gray-700 mb-1">Nội dung header (ví dụ:
                    thời gian mắc đái tháo đường...)</label>
                <textarea name="header_content" id="header_content" rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">{{ old('header_content', isset($news) ? $news->header_content : '') }}</textarea>
                <p class="mt-1 text-xs text-gray-500">Nội dung này sẽ hiển thị ở đầu bài viết, trước hình ảnh chính</p>
                @error('header_content')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Main Content -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Nội dung chính <span
                        class="text-red-500">*</span></label>
                <textarea name="content" id="content" rows="10"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">{{ old('content', isset($news) ? $news->content : '') }}</textarea>
                @error('content')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Excerpt -->
            <div>
                <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-1">Tóm tắt</label>
                <textarea name="excerpt" id="excerpt" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">{{ old('excerpt', isset($news) ? $news->excerpt : '') }}</textarea>
                <p class="mt-1 text-xs text-gray-500">Để trống để tự động tạo từ nội dung header hoặc nội dung chính</p>
                @error('excerpt')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <a href="{{ route('admin.news') }}"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Hủy
            </a>
            <button type="submit"
                class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                {{ isset($news) ? 'Cập nhật' : 'Tạo mới' }}
            </button>
        </div>
    </form>
</div>

<!-- CKEditor 5 -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
    // Custom upload adapter
    class MyUploadAdapter {
        constructor(loader) {
            this.loader = loader;
        }

        upload() {
            return this.loader.file
                .then(file => new Promise((resolve, reject) => {
                    this._initRequest();
                    this._initListeners(resolve, reject, file);
                    this._sendRequest(file);
                }));
        }

        abort() {
            if (this.xhr) {
                this.xhr.abort();
            }
        }

        _initRequest() {
            const xhr = this.xhr = new XMLHttpRequest();
            xhr.open('POST', '{{ route("admin.news.upload-image") }}', true);
            xhr.setRequestHeader('X-CSRF-TOKEN', '{{ csrf_token() }}');
            xhr.responseType = 'json';
        }

        _initListeners(resolve, reject, file) {
            const xhr = this.xhr;
            const loader = this.loader;
            const genericErrorText = `Couldn't upload file: ${file.name}.`;

            xhr.addEventListener('error', () => {
                console.error('Upload error:', xhr.statusText);
                reject(genericErrorText);
            });
            xhr.addEventListener('abort', () => reject());
            xhr.addEventListener('load', () => {
                const response = xhr.response;
                console.log('Upload response:', response);

                if (!response || xhr.status !== 200) {
                    console.error('Upload failed:', response);
                    return reject(response && response.message ? response.message : genericErrorText);
                }

                if (!response.uploaded) {
                    console.error('Upload not successful:', response.error);
                    return reject(response.error && response.error.message ? response.error.message : genericErrorText);
                }

                console.log('Upload successful, URL:', response.url);
                resolve({
                    default: response.url,
                    '160': response.url,
                    '500': response.url,
                    '1000': response.url,
                    '1052': response.url
                });
            });

            if (xhr.upload) {
                xhr.upload.addEventListener('progress', evt => {
                    if (evt.lengthComputable) {
                        loader.uploadTotal = evt.total;
                        loader.uploaded = evt.loaded;
                    }
                });
            }
        }

        _sendRequest(file) {
            const data = new FormData();
            data.append('upload', file);
            this.xhr.send(data);
        }
    }

    function MyCustomUploadAdapterPlugin(editor) {
        editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
            return new MyUploadAdapter(loader);
        };
    }

    document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor for header content
    ClassicEditor
        .create(document.querySelector('#header_content'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', '|', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'imageUpload', 'blockQuote', 'insertTable', 'undo', 'redo'],
            image: {
                toolbar: ['imageTextAlternative', 'imageStyle:full', 'imageStyle:side', 'imageStyle:alignLeft', 'imageStyle:alignCenter', 'imageStyle:alignRight'],
                styles: [
                    'full',
                    'side',
                    'alignLeft',
                    'alignCenter',
                    'alignRight'
                ]
            },
            extraPlugins: [MyCustomUploadAdapterPlugin]
        })
        .then(editor => {
            console.log('Header content editor initialized successfully');
            window.headerEditor = editor;
        })
        .catch(error => {
            console.error('Error initializing header content editor:', error);
        });

    // Initialize CKEditor for main content
    ClassicEditor
        .create(document.querySelector('#content'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', '|', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'imageUpload', 'blockQuote', 'insertTable', 'mediaEmbed', '|', 'undo', 'redo'],
            image: {
                toolbar: ['imageTextAlternative', 'imageStyle:full', 'imageStyle:side', 'imageStyle:alignLeft', 'imageStyle:alignCenter', 'imageStyle:alignRight'],
                styles: [
                    'full',
                    'side',
                    'alignLeft',
                    'alignCenter',
                    'alignRight'
                ]
            },
            mediaEmbed: {
                previewsInData: true
            },
            extraPlugins: [MyCustomUploadAdapterPlugin]
        })
        .then(editor => {
            console.log('Main content editor initialized successfully');
            window.contentEditor = editor;
        })
        .catch(error => {
            console.error('Error initializing main content editor:', error);
        });

    // Handle form submission
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        // Update textareas with CKEditor content before submission
        if (window.headerEditor) {
            document.querySelector('#header_content').value = window.headerEditor.getData();
        }
        if (window.contentEditor) {
            const contentData = window.contentEditor.getData();
            document.querySelector('#content').value = contentData;

            // Validate content is not empty
            if (!contentData.trim()) {
                e.preventDefault();
                alert('Vui lòng nhập nội dung chính cho bài viết');
                return false;
            }
        }
    });

    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');

    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.dataset.autoGenerated !== 'false') {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
            slugInput.dataset.autoGenerated = 'true';
        }
    });

    slugInput.addEventListener('input', function() {
        this.dataset.autoGenerated = 'false';
    });

    // Show/hide YouTube link field based on type
    const typeSelect = document.getElementById('type');
    const youtubeLinkSection = document.getElementById('youtube-link-section');
    const youtubeLinkInput = document.getElementById('youtube_link');

    function toggleYouTubeField() {
        if (typeSelect.value === 'video') {
            youtubeLinkSection.style.display = 'block';
            youtubeLinkInput.required = true;
        } else {
            youtubeLinkSection.style.display = 'none';
            youtubeLinkInput.required = false;
        }
    }

    // Initial check
    toggleYouTubeField();

    // Listen for changes
    typeSelect.addEventListener('change', toggleYouTubeField);
});
</script>
@endsection