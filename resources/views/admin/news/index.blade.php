@extends('admin.layouts.app')

@section('title', 'Quản lý tin tức')
@section('header', 'Quản lý tin tức')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900"><PERSON><PERSON> s<PERSON>ch tin tức</h3>
        <div class="flex space-x-2">
            <a href="{{ route('admin.news.create') }}"
                class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-plus mr-1"></i> Thêm mới
            </a>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                    class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-filter mr-1"></i> Lọc
                </button>
                <div x-show="open" @click.away="open = false"
                    class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100"
                    style="display: none;">
                    <div class="py-1">
                        <a href="{{ route('admin.news', ['status' => 'all']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tất cả</a>
                        <a href="{{ route('admin.news', ['status' => 'published']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đã xuất bản</a>
                        <a href="{{ route('admin.news', ['status' => 'draft']) }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Bản nháp</a>
                    </div>
                </div>
            </div>
            <form method="GET" action="{{ route('admin.news') }}" class="flex">
                <input type="text" name="search" placeholder="Tìm kiếm..." value="{{ request('search') }}"
                    class="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-primary focus:border-primary">
                <button type="submit"
                    class="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hình
                            ảnh</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu
                            đề</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh
                            mục</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nổi
                            bật</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày
                            tạo</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng
                            thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao
                            tác</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($news as $article)
                    <tr>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $article->id }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($article->thumbnail)
                            <img src="{{ asset($article->thumbnail) }}" alt="{{ $article->title }}"
                                class="h-10 w-10 rounded-md object-cover">
                            @else
                            <div class="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                            @endif
                        </td>
                        <td class="px-4 py-3">
                            <div class="text-sm text-gray-900 line-clamp-2">{{ $article->title }}</div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $article->category }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($article->type == 'video')
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                <i class="fas fa-video mr-1"></i> Video
                            </span>
                            @else
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                <i class="fas fa-file-alt mr-1"></i> Bài viết
                            </span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($article->featured)
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                Nổi bật
                            </span>
                            @else
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                Thường
                            </span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $article->created_at->format('d/m/Y') }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $article->status == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $article->status == 'published' ? 'Đã xuất bản' : 'Bản nháp' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                            <a href="{{ route('admin.news.edit', $article->id) }}"
                                class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('admin.news.delete', $article->id) }}" method="POST"
                                class="inline-block">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900"
                                    onclick="return confirm('Bạn có chắc chắn muốn xóa bài viết này?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="px-4 py-3 text-center text-gray-500">Không có bài viết nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $news->links() }}
        </div>
    </div>
</div>
@endsection