@extends('admin.layouts.app')

@section('title', 'Cài đặt hệ thống')
@section('header', 'Cài đặt hệ thống')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Cà<PERSON> đặt hệ thống</h3>
    </div>

    <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data" class="p-6">
        @csrf
        @method('PUT')

        <div x-data="{ activeTab: 'banner' }" class="space-y-6">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button type="button" @click="activeTab = 'banner'"
                        :class="{ 'border-primary text-primary': activeTab === 'banner', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'banner' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Banner
                    </button>
                    <button type="button" @click="activeTab = 'general'"
                        :class="{ 'border-primary text-primary': activeTab === 'general', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'general' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Thông tin chung
                    </button>
                    <button type="button" @click="activeTab = 'contact'"
                        :class="{ 'border-primary text-primary': activeTab === 'contact', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'contact' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Thông tin liên hệ
                    </button>
                    <button type="button" @click="activeTab = 'social'"
                        :class="{ 'border-primary text-primary': activeTab === 'social', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'social' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Mạng xã hội
                    </button>
                    <button type="button" @click="activeTab = 'content'"
                        :class="{ 'border-primary text-primary': activeTab === 'content', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'content' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Nội dung
                    </button>
                    <button type="button" @click="activeTab = 'seo'"
                        :class="{ 'border-primary text-primary': activeTab === 'seo', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'seo' }"
                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        SEO & Analytics
                    </button>
                </nav>
            </div>

            <!-- Banner Settings Tab -->
            <div x-show="activeTab === 'banner'" class="space-y-6">
                <div class="space-y-6">
                    <!-- File Size Warning -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Lưu ý về kích thước file</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>Kích thước file tối đa: 5MB. Khuyến nghị sử dụng định dạng JPG hoặc PNG để có
                                        chất lượng tốt nhất.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($errors->has('file_size'))
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Lỗi upload file</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>{{ $errors->first('file_size') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Banner Desktop Image -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Banner Desktop
                            <span class="text-gray-500">(Khuyến nghị: 1920x600px, tối đa 5MB)</span>
                        </label>

                        @php
                        $desktopImage = \App\Models\Setting::get('banner_desktop_image');
                        @endphp

                        @if($desktopImage)
                        <div class="mb-4">
                            <img src="{{ asset($desktopImage) }}" alt="Banner Desktop"
                                class="max-w-md h-32 object-cover rounded-lg border">
                            <div class="mt-2">
                                <button type="button" onclick="deleteBannerImage('desktop')"
                                    class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash mr-1"></i> Xóa hình ảnh
                                </button>
                            </div>
                        </div>
                        @endif

                        <input type="file" name="banner_desktop_image" accept="image/*"
                            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark">
                        @error('banner_desktop_image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Banner Mobile Image -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Banner Mobile
                            <span class="text-gray-500">(Khuyến nghị: 768x400px, tối đa 5MB)</span>
                        </label>

                        @php
                        $mobileImage = \App\Models\Setting::get('banner_mobile_image');
                        @endphp

                        @if($mobileImage)
                        <div class="mb-4">
                            <img src="{{ asset($mobileImage) }}" alt="Banner Mobile"
                                class="max-w-md h-32 object-cover rounded-lg border">
                            <div class="mt-2">
                                <button type="button" onclick="deleteBannerImage('mobile')"
                                    class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash mr-1"></i> Xóa hình ảnh
                                </button>
                            </div>
                        </div>
                        @endif

                        <input type="file" name="banner_mobile_image" accept="image/*"
                            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark">
                        @error('banner_mobile_image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Banner Title -->
                    <div>
                        <label for="banner_title" class="block text-sm font-medium text-gray-700 mb-1">Tiêu đề
                            Banner</label>
                        <input type="text" name="banner_title" id="banner_title"
                            value="{{ old('banner_title', \App\Models\Setting::get('banner_title', 'Chăm sóc sức khỏe toàn diện')) }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        @error('banner_title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Banner Subtitle -->
                    <div>
                        <label for="banner_subtitle" class="block text-sm font-medium text-gray-700 mb-1">Phụ đề
                            Banner</label>
                        <textarea name="banner_subtitle" id="banner_subtitle" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                            placeholder="Mô tả ngắn về dịch vụ...">{{ old('banner_subtitle', \App\Models\Setting::get('banner_subtitle', 'Đội ngũ bác sĩ chuyên nghiệp, trang thiết bị hiện đại')) }}</textarea>
                        @error('banner_subtitle')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- General Settings Tab -->
            <div x-show="activeTab === 'general'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Tên website
                        </label>
                        <input type="text" id="site_name" name="site_name"
                            value="{{ old('site_name', $settings['site_name'] ?? '') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('site_name') border-red-500 @enderror">
                        @error('site_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Mô tả website
                        </label>
                        <input type="text" id="site_description" name="site_description"
                            value="{{ old('site_description', $settings['site_description'] ?? '') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('site_description') border-red-500 @enderror">
                        @error('site_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="site_logo" class="block text-sm font-medium text-gray-700 mb-2">
                            Logo website
                        </label>
                        <input type="file" id="site_logo" name="site_logo" accept="image/*"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('site_logo') border-red-500 @enderror">
                        @error('site_logo')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @if(isset($settings['site_logo']) && $settings['site_logo'])
                        <div class="mt-2">
                            <img src="{{ Storage::url($settings['site_logo']) }}" alt="Current logo"
                                class="h-16 w-auto">
                        </div>
                        @endif
                    </div>

                    <div>
                        <label for="site_favicon" class="block text-sm font-medium text-gray-700 mb-2">
                            Favicon
                        </label>
                        <input type="file" id="site_favicon" name="site_favicon" accept=".ico,.png"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('site_favicon') border-red-500 @enderror">
                        @error('site_favicon')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @if(isset($settings['site_favicon']) && $settings['site_favicon'])
                        <div class="mt-2">
                            <img src="{{ Storage::url($settings['site_favicon']) }}" alt="Current favicon"
                                class="h-8 w-8">
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Contact Settings Tab -->
            <div x-show="activeTab === 'contact'" class="space-y-6" style="display: none;">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email liên hệ
                        </label>
                        <input type="email" id="contact_email" name="contact_email"
                            value="{{ old('contact_email', $settings['contact_email'] ?? '') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('contact_email') border-red-500 @enderror">
                        @error('contact_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Số điện thoại
                        </label>
                        <input type="text" id="contact_phone" name="contact_phone"
                            value="{{ old('contact_phone', $settings['contact_phone'] ?? '') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('contact_phone') border-red-500 @enderror">
                        @error('contact_phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="contact_address" class="block text-sm font-medium text-gray-700 mb-2">
                            Địa chỉ
                        </label>
                        <textarea id="contact_address" name="contact_address" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('contact_address') border-red-500 @enderror">{{ old('contact_address', $settings['contact_address'] ?? '') }}</textarea>
                        @error('contact_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="working_hours" class="block text-sm font-medium text-gray-700 mb-2">
                            Giờ làm việc
                        </label>
                        <input type="text" id="working_hours" name="working_hours"
                            value="{{ old('working_hours', $settings['working_hours'] ?? '') }}"
                            placeholder="VD: Thứ 2 - Thứ 6: 8:00 - 17:00"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('working_hours') border-red-500 @enderror">
                        @error('working_hours')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="appointment_slots" class="block text-sm font-medium text-gray-700 mb-2">
                            Khung giờ đặt lịch
                        </label>
                        <input type="text" id="appointment_slots" name="appointment_slots"
                            value="{{ old('appointment_slots', $settings['appointment_slots'] ?? '') }}"
                            placeholder="08:00,09:00,10:00,11:00,14:00,15:00,16:00"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('appointment_slots') border-red-500 @enderror">
                        @error('appointment_slots')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Nhập các khung giờ cách nhau bởi dấu phẩy</p>
                    </div>
                </div>
            </div>

            <!-- Social Media Tab -->
            <div x-show="activeTab === 'social'" class="space-y-6" style="display: none;">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="facebook_url" class="block text-sm font-medium text-gray-700 mb-2">
                            Facebook URL
                        </label>
                        <input type="url" id="facebook_url" name="facebook_url"
                            value="{{ old('facebook_url', $settings['facebook_url'] ?? '') }}"
                            placeholder="https://facebook.com/..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('facebook_url') border-red-500 @enderror">
                        @error('facebook_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                            YouTube URL
                        </label>
                        <input type="url" id="youtube_url" name="youtube_url"
                            value="{{ old('youtube_url', $settings['youtube_url'] ?? '') }}"
                            placeholder="https://youtube.com/..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('youtube_url') border-red-500 @enderror">
                        @error('youtube_url')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Content Tab -->
            <div x-show="activeTab === 'content'" class="space-y-6" style="display: none;">
                <div>
                    <label for="about_content" class="block text-sm font-medium text-gray-700 mb-2">
                        Nội dung trang Giới thiệu
                    </label>
                    <textarea id="about_content" name="about_content" rows="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('about_content') border-red-500 @enderror">{{ old('about_content', $settings['about_content'] ?? '') }}</textarea>
                    @error('about_content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="services_content" class="block text-sm font-medium text-gray-700 mb-2">
                        Nội dung trang Dịch vụ
                    </label>
                    <textarea id="services_content" name="services_content" rows="6"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('services_content') border-red-500 @enderror">{{ old('services_content', $settings['services_content'] ?? '') }}</textarea>
                    @error('services_content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- SEO & Analytics Tab -->
            <div x-show="activeTab === 'seo'" class="space-y-6" style="display: none;">
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Keywords
                        </label>
                        <input type="text" id="meta_keywords" name="meta_keywords"
                            value="{{ old('meta_keywords', $settings['meta_keywords'] ?? '') }}"
                            placeholder="từ khóa 1, từ khóa 2, từ khóa 3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('meta_keywords') border-red-500 @enderror">
                        @error('meta_keywords')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Description
                        </label>
                        <textarea id="meta_description" name="meta_description" rows="3" maxlength="160"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('meta_description') border-red-500 @enderror">{{ old('meta_description', $settings['meta_description'] ?? '') }}</textarea>
                        @error('meta_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Tối đa 160 ký tự</p>
                    </div>

                    <div>
                        <label for="google_analytics_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Google Analytics ID
                        </label>
                        <input type="text" id="google_analytics_id" name="google_analytics_id"
                            value="{{ old('google_analytics_id', $settings['google_analytics_id'] ?? '') }}"
                            placeholder="G-XXXXXXXXXX"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('google_analytics_id') border-red-500 @enderror">
                        @error('google_analytics_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="facebook_pixel_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Facebook Pixel ID
                        </label>
                        <input type="text" id="facebook_pixel_id" name="facebook_pixel_id"
                            value="{{ old('facebook_pixel_id', $settings['facebook_pixel_id'] ?? '') }}"
                            placeholder="123456789012345"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('facebook_pixel_id') border-red-500 @enderror">
                        @error('facebook_pixel_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>


        </div>

        <div class="mt-8 pt-6 border-t border-gray-200">
            <div class="flex justify-end">
                <button type="submit"
                    class="px-6 py-2 bg-primary border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-save mr-1"></i>
                    Lưu cài đặt
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    function deleteBannerImage(type) {
    if (confirm('Bạn có chắc chắn muốn xóa hình ảnh này?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.settings.delete-banner") }}';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        const typeField = document.createElement('input');
        typeField.type = 'hidden';
        typeField.name = 'type';
        typeField.value = type;

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        form.appendChild(typeField);

        document.body.appendChild(form);
        form.submit();
    }
}

// Preview uploaded images
document.addEventListener('DOMContentLoaded', function() {
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');

    imageInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create preview if doesn't exist
                    let preview = input.parentNode.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'image-preview mt-2';
                        input.parentNode.insertBefore(preview, input.nextSibling);
                    }

                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview" class="max-w-md h-32 object-cover rounded-lg border">
                        <p class="text-sm text-gray-600 mt-1">Preview: ${file.name}</p>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    });
});
</script>
@endsection