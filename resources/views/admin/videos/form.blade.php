@extends('admin.layouts.app')

@section('title', isset($video) ? 'Chỉnh sửa Video' : 'Thêm Video mới')
@section('header', isset($video) ? 'Chỉnh sửa Video' : 'Thêm Video mới')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
            {{ isset($video) ? 'Chỉnh sửa Video' : 'Thêm Video mới' }}
        </h3>
    </div>
    
    <form method="POST" action="{{ isset($video) ? route('admin.videos.update', $video->id) : route('admin.videos.store') }}" class="p-6">
        @csrf
        @if(isset($video))
            @method('PUT')
        @endif
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Ti<PERSON>u đ<PERSON> <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $video->title ?? '') }}"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('title') border-red-500 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                            YouTube URL <span class="text-red-500">*</span>
                        </label>
                        <input type="url" 
                               id="youtube_url" 
                               name="youtube_url" 
                               value="{{ old('youtube_url', $video->youtube_url ?? '') }}"
                               required
                               placeholder="https://www.youtube.com/watch?v=..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('youtube_url') border-red-500 @enderror">
                        @error('youtube_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">Nhập URL video YouTube (hỗ trợ cả youtube.com và youtu.be)</p>
                    </div>
                    
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Mô tả
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('description') border-red-500 @enderror">{{ old('description', $video->description ?? '') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    @if(isset($video) && $video->youtube_id)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Xem trước Video
                        </label>
                        <div class="aspect-w-16 aspect-h-9">
                            <iframe src="{{ $video->embed_url }}" 
                                    frameborder="0" 
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                    allowfullscreen
                                    class="w-full h-64 rounded-lg"></iframe>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            
            <div class="lg:col-span-1">
                <div class="space-y-6">
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                            Danh mục
                        </label>
                        <input type="text" 
                               id="category" 
                               name="category" 
                               value="{{ old('category', $video->category ?? '') }}"
                               placeholder="VD: Sức khỏe, Tư vấn, Hướng dẫn..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('category') border-red-500 @enderror">
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Trạng thái <span class="text-red-500">*</span>
                        </label>
                        <select id="status" 
                                name="status" 
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary @error('status') border-red-500 @enderror">
                            <option value="draft" {{ old('status', $video->status ?? '') == 'draft' ? 'selected' : '' }}>
                                Bản nháp
                            </option>
                            <option value="published" {{ old('status', $video->status ?? '') == 'published' ? 'selected' : '' }}>
                                Đã xuất bản
                            </option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="featured" 
                                   name="featured" 
                                   value="1"
                                   {{ old('featured', $video->featured ?? false) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            <label for="featured" class="ml-2 block text-sm text-gray-900">
                                Video nổi bật
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Video nổi bật sẽ được hiển thị ưu tiên trên trang chủ</p>
                        @error('featured')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    @if(isset($video))
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Thống kê</h4>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>Lượt xem:</span>
                                <span class="font-medium">{{ number_format($video->views) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Ngày tạo:</span>
                                <span class="font-medium">{{ $video->created_at->format('d/m/Y H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Cập nhật:</span>
                                <span class="font-medium">{{ $video->updated_at->format('d/m/Y H:i') }}</span>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
            <a href="{{ route('admin.videos') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Hủy
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary border border-transparent rounded-md text-sm font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                {{ isset($video) ? 'Cập nhật' : 'Tạo mới' }}
            </button>
        </div>
    </form>
</div>
@endsection
