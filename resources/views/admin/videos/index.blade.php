@extends('admin.layouts.app')

@section('title', 'Quản lý Videos')
@section('header', 'Quản lý Videos')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900"><PERSON>h sách Videos</h3>
        <div class="flex space-x-2">
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-filter mr-1"></i> Lọc
                </button>
                <div x-show="open" @click.away="open = false" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100" style="display: none;">
                    <div class="py-1">
                        <a href="{{ route('admin.videos', ['status' => 'all']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tất cả</a>
                        <a href="{{ route('admin.videos', ['status' => 'published']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Đã xuất bản</a>
                        <a href="{{ route('admin.videos', ['status' => 'draft']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Bản nháp</a>
                    </div>
                </div>
            </div>
            <form method="GET" action="{{ route('admin.videos') }}" class="flex">
                <input type="text" name="search" placeholder="Tìm kiếm..." value="{{ request('search') }}" class="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-primary focus:border-primary">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <a href="{{ route('admin.videos.create') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-plus mr-1"></i> Thêm Video
            </a>
        </div>
    </div>
    <div class="p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lượt xem</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nổi bật</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($videos as $video)
                    <tr>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($video->thumbnail)
                                <img src="{{ $video->thumbnail }}" alt="{{ $video->title }}" class="h-12 w-20 object-cover rounded">
                            @else
                                <div class="h-12 w-20 bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-video text-gray-400"></i>
                                </div>
                            @endif
                        </td>
                        <td class="px-4 py-3">
                            <div class="text-sm font-medium text-gray-900">{{ Str::limit($video->title, 50) }}</div>
                            @if($video->description)
                                <div class="text-sm text-gray-500">{{ Str::limit($video->description, 80) }}</div>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($video->category)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ $video->category }}
                                </span>
                            @else
                                <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {{ number_format($video->views) }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $video->status == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ $video->status == 'published' ? 'Đã xuất bản' : 'Bản nháp' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($video->featured)
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                    <i class="fas fa-star mr-1"></i> Nổi bật
                                </span>
                            @else
                                <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                            {{ $video->created_at->format('d/m/Y') }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                            <a href="{{ $video->watch_url }}" target="_blank" class="text-blue-600 hover:text-blue-900 mr-3" title="Xem trên YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="{{ route('admin.videos.show', $video->id) }}" class="text-primary hover:text-primary-dark mr-3" title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ route('admin.videos.edit', $video->id) }}" class="text-yellow-600 hover:text-yellow-900 mr-3" title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" action="{{ route('admin.videos.destroy', $video->id) }}" class="inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa video này?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900" title="Xóa">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="px-4 py-3 text-center text-gray-500">Không có video nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $videos->links() }}
        </div>
    </div>
</div>
@endsection
