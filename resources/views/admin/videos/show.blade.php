@extends('admin.layouts.app')

@section('title', 'Chi tiết Video')
@section('header', 'Chi tiết Video')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Chi tiết Video</h3>
        <div class="flex space-x-2">
            <a href="{{ $video->watch_url }}" target="_blank" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fab fa-youtube mr-1"></i> Xem trên YouTube
            </a>
            <a href="{{ route('admin.videos.edit', $video->id) }}" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                <i class="fas fa-edit mr-1"></i> Chỉnh sửa
            </a>
            <a href="{{ route('admin.videos') }}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-arrow-left mr-1"></i> Quay lại
            </a>
        </div>
    </div>
    
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $video->title }}</h1>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span>
                                <i class="fas fa-eye mr-1"></i>
                                {{ number_format($video->views) }} lượt xem
                            </span>
                            <span>
                                <i class="fas fa-calendar mr-1"></i>
                                {{ $video->created_at->format('d/m/Y H:i') }}
                            </span>
                            @if($video->category)
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                {{ $video->category }}
                            </span>
                            @endif
                        </div>
                    </div>
                    
                    @if($video->youtube_id)
                    <div>
                        <div class="aspect-w-16 aspect-h-9">
                            <iframe src="{{ $video->embed_url }}" 
                                    frameborder="0" 
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                    allowfullscreen
                                    class="w-full h-96 rounded-lg shadow-lg"></iframe>
                        </div>
                    </div>
                    @endif
                    
                    @if($video->description)
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Mô tả</h3>
                        <div class="prose max-w-none">
                            {!! nl2br(e($video->description)) !!}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            
            <div class="lg:col-span-1">
                <div class="space-y-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Thông tin Video</h4>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm text-gray-500">Trạng thái:</span>
                                <div class="mt-1">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ $video->status == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $video->status == 'published' ? 'Đã xuất bản' : 'Bản nháp' }}
                                    </span>
                                </div>
                            </div>
                            
                            @if($video->featured)
                            <div>
                                <span class="text-sm text-gray-500">Đặc biệt:</span>
                                <div class="mt-1">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                        <i class="fas fa-star mr-1"></i> Video nổi bật
                                    </span>
                                </div>
                            </div>
                            @endif
                            
                            <div>
                                <span class="text-sm text-gray-500">YouTube ID:</span>
                                <div class="mt-1 text-sm font-mono text-gray-900">{{ $video->youtube_id }}</div>
                            </div>
                            
                            <div>
                                <span class="text-sm text-gray-500">Người tạo:</span>
                                <div class="mt-1 text-sm text-gray-900">{{ $video->admin->name ?? 'N/A' }}</div>
                            </div>
                            
                            <div>
                                <span class="text-sm text-gray-500">Ngày tạo:</span>
                                <div class="mt-1 text-sm text-gray-900">{{ $video->created_at->format('d/m/Y H:i:s') }}</div>
                            </div>
                            
                            <div>
                                <span class="text-sm text-gray-500">Cập nhật lần cuối:</span>
                                <div class="mt-1 text-sm text-gray-900">{{ $video->updated_at->format('d/m/Y H:i:s') }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-3">Thống kê</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-blue-700">Lượt xem:</span>
                                <span class="text-lg font-bold text-blue-900">{{ number_format($video->views) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Liên kết</h4>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">YouTube URL:</span>
                                <div class="mt-1">
                                    <a href="{{ $video->youtube_url }}" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 break-all">
                                        {{ Str::limit($video->youtube_url, 40) }}
                                    </a>
                                </div>
                            </div>
                            
                            @if($video->thumbnail)
                            <div>
                                <span class="text-sm text-gray-500">Thumbnail:</span>
                                <div class="mt-1">
                                    <img src="{{ $video->thumbnail }}" alt="{{ $video->title }}" class="w-full rounded border">
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
