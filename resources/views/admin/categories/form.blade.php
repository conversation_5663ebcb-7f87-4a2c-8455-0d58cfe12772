@extends('admin.layouts.app')

@section('title', isset($category) ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới')
@section('header', isset($category) ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ isset($category) ? 'Chỉnh sửa danh mục' : 'Thêm danh mục mới' }}</h3>
    </div>
    <form action="{{ isset($category) ? route('admin.categories.update', $category->id) : route('admin.categories.store') }}" method="POST">
        @csrf
        @if(isset($category))
            @method('PUT')
        @endif
        
        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Tên danh mục <span class="text-red-500">*</span></label>
                    <input type="text" name="name" id="name" value="{{ old('name', isset($category) ? $category->name : '') }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                    <input type="text" name="slug" id="slug" value="{{ old('slug', isset($category) ? $category->slug : '') }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Để trống để tự động tạo">
                    @error('slug')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="background_color" class="block text-sm font-medium text-gray-700 mb-1">Màu nền <span class="text-red-500">*</span></label>
                    <div class="flex items-center space-x-2">
                        <input type="color" name="background_color" id="background_color" value="{{ old('background_color', isset($category) ? $category->background_color : '#FFF4E3') }}" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                        <input type="text" id="background_color_text" value="{{ old('background_color', isset($category) ? $category->background_color : '#FFF4E3') }}" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" readonly>
                    </div>
                    @error('background_color')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="border_color" class="block text-sm font-medium text-gray-700 mb-1">Màu viền <span class="text-red-500">*</span></label>
                    <div class="flex items-center space-x-2">
                        <input type="color" name="border_color" id="border_color" value="{{ old('border_color', isset($category) ? $category->border_color : '#FFDEAC') }}" class="w-12 h-10 border border-gray-300 rounded cursor-pointer">
                        <input type="text" id="border_color_text" value="{{ old('border_color', isset($category) ? $category->border_color : '#FFDEAC') }}" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" readonly>
                    </div>
                    @error('border_color')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Trạng thái <span class="text-red-500">*</span></label>
                    <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" required>
                        <option value="active" {{ old('status', isset($category) ? $category->status : 'active') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                        <option value="inactive" {{ old('status', isset($category) ? $category->status : '') == 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">Thứ tự sắp xếp</label>
                    <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', isset($category) ? $category->sort_order : 0) }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" min="0">
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
                <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Mô tả về danh mục...">{{ old('description', isset($category) ? $category->description : '') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <!-- Preview -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Xem trước</label>
                <div id="category-preview" class="inline-block px-4 py-2 rounded-full border-2 text-sm font-medium" style="background-color: {{ old('background_color', isset($category) ? $category->background_color : '#FFF4E3') }}; border-color: {{ old('border_color', isset($category) ? $category->border_color : '#FFDEAC') }};">
                    <span id="preview-text">{{ old('name', isset($category) ? $category->name : 'Tên danh mục') }}</span>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <a href="{{ route('admin.categories') }}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Hủy
            </a>
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                {{ isset($category) ? 'Cập nhật' : 'Tạo mới' }}
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    const backgroundColorInput = document.getElementById('background_color');
    const borderColorInput = document.getElementById('border_color');
    const backgroundColorText = document.getElementById('background_color_text');
    const borderColorText = document.getElementById('border_color_text');
    const preview = document.getElementById('category-preview');
    const previewText = document.getElementById('preview-text');
    
    // Auto-generate slug from name
    nameInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === slugify(nameInput.dataset.oldValue || '')) {
            slugInput.value = slugify(this.value);
        }
        nameInput.dataset.oldValue = this.value;
        updatePreview();
    });
    
    // Update color text inputs
    backgroundColorInput.addEventListener('input', function() {
        backgroundColorText.value = this.value.toUpperCase();
        updatePreview();
    });
    
    borderColorInput.addEventListener('input', function() {
        borderColorText.value = this.value.toUpperCase();
        updatePreview();
    });
    
    // Update preview
    function updatePreview() {
        previewText.textContent = nameInput.value || 'Tên danh mục';
        preview.style.backgroundColor = backgroundColorInput.value;
        preview.style.borderColor = borderColorInput.value;
    }
    
    // Slugify function
    function slugify(text) {
        return text
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
    }
});
</script>
@endsection
