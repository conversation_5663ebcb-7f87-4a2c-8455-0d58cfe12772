<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Admin Dashboard') - BS Lý Đại <PERSON></title>
    <link href="{{ mix('css/app.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="//unpkg.com/alpinejs" defer></script>
</head>

<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div :class="{'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen}"
            class="fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0">
            <div class="flex items-center justify-center h-16 bg-primary">
                <span class="text-white text-lg font-bold">BS Lý Đại Lương</span>
            </div>
            <nav class="mt-5 px-2">
                {{-- <a href="{{ route('admin.dashboard') }}"
                    class="group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.dashboard') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-tachometer-alt mr-3 text-gray-500"></i>
                    Dashboard
                </a> --}}
                <a href="{{ route('admin.appointments') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.appointments*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-calendar-alt mr-3 text-gray-500"></i>
                    Lịch hẹn
                </a>
                <a href="{{ route('admin.daily-time-slots') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.daily-time-slots*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-calendar-check mr-3 text-gray-500"></i>
                    Khung Giờ Khám
                </a>
                {{-- <a href="{{ route('admin.questions') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.questions*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-question-circle mr-3 text-gray-500"></i>
                    Câu hỏi
                </a> --}}
                <a href="{{ route('admin.news') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.news*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-newspaper mr-3 text-gray-500"></i>
                    Tin tức
                </a>
                <a href="{{ route('admin.categories') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.categories*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-tags mr-3 text-gray-500"></i>
                    Danh mục
                </a>
                <a href="{{ route('admin.medical-topics') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.medical-topics*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-stethoscope mr-3 text-gray-500"></i>
                    Chủ đề y tế
                </a>
                {{-- <a href="{{ route('admin.videos') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.videos*') ? 'bg-gray-100' : '' }}">
                    <i class="fab fa-youtube mr-3 text-gray-500"></i>
                    Videos
                </a> --}}

                <a href="{{ route('admin.settings') }}"
                    class="mt-1 group flex items-center px-2 py-2 text-base leading-6 font-medium rounded-md text-gray-900 hover:bg-gray-100 {{ request()->routeIs('admin.settings*') ? 'bg-gray-100' : '' }}">
                    <i class="fas fa-cog mr-3 text-gray-500"></i>
                    Cài đặt
                </a>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="bg-white shadow-sm z-10">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex">
                            <button @click="sidebarOpen = !sidebarOpen"
                                class="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary md:hidden">
                                <i class="fas fa-bars"></i>
                            </button>
                            <div class="flex-shrink-0 flex items-center">
                                <h1 class="text-lg font-semibold">@yield('header', 'Dashboard')</h1>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="ml-3 relative" x-data="{ open: false }">
                                <div>
                                    <button @click="open = !open"
                                        class="max-w-xs flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                        id="user-menu" aria-haspopup="true">
                                        <span class="sr-only">Open user menu</span>
                                        <span
                                            class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary">
                                            <span class="text-sm font-medium leading-none text-white">{{
                                                auth('admin')->user()->name[0] ?? 'A' }}</span>
                                        </span>
                                    </button>
                                </div>
                                <div x-show="open" @click.away="open = false"
                                    x-transition:enter="transition ease-out duration-100"
                                    x-transition:enter-start="transform opacity-0 scale-95"
                                    x-transition:enter-end="transform opacity-100 scale-100"
                                    x-transition:leave="transition ease-in duration-75"
                                    x-transition:leave-start="transform opacity-100 scale-100"
                                    x-transition:leave-end="transform opacity-0 scale-95"
                                    class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5"
                                    role="menu" aria-orientation="vertical" aria-labelledby="user-menu"
                                    style="display: none;">

                                    <a href="{{ route('admin.settings') }}"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        role="menuitem">Cài đặt</a>
                                    <a href="{{ route('admin.profile.change-password') }}"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                        role="menuitem">Đổi mật khẩu</a>
                                    <form method="POST" action="{{ route('admin.logout') }}">
                                        @csrf
                                        <button type="submit"
                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            role="menuitem">Đăng xuất</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-y-auto p-4 bg-gray-50">
                @yield('content')
            </main>
        </div>
    </div>
</body>

</html>