@extends('admin.layouts.app')

@section('title', '<PERSON><PERSON>ản lý chủ đề y tế')
@section('header', 'Quản lý chủ đề y tế')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">Danh sách chủ đề y tế</h3>
        <div class="flex space-x-2">
            <a href="{{ route('admin.medical-topics.create') }}" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="fas fa-plus mr-1"></i> Thêm mới
            </a>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-filter mr-1"></i> Lọc
                </button>
                <div x-show="open" @click.away="open = false" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100" style="display: none;">
                    <div class="py-1">
                        <a href="{{ route('admin.medical-topics', ['status' => 'all']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tất cả trạng thái</a>
                        <a href="{{ route('admin.medical-topics', ['status' => 'active']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Hoạt động</a>
                        <a href="{{ route('admin.medical-topics', ['status' => 'inactive']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Không hoạt động</a>
                    </div>
                    <div class="py-1">
                        <a href="{{ route('admin.medical-topics', ['category_id' => 'all']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tất cả danh mục</a>
                        @foreach($categories as $category)
                        <a href="{{ route('admin.medical-topics', ['category_id' => $category->id]) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ $category->name }}</a>
                        @endforeach
                    </div>
                </div>
            </div>
            <form method="GET" action="{{ route('admin.medical-topics') }}" class="flex">
                <input type="text" name="search" placeholder="Tìm kiếm..." value="{{ request('search') }}" class="px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-primary focus:border-primary">
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="p-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Danh mục</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thứ tự</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nổi bật</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($topics as $topic)
                    <tr>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $topic->id }}</td>
                        <td class="px-4 py-3">
                            <div class="text-sm text-gray-900 font-medium">{{ $topic->title }}</div>
                            @if($topic->description)
                            <div class="text-sm text-gray-500">{{ Str::limit($topic->description, 80) }}</div>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($topic->category)
                            <div class="inline-block px-3 py-1 rounded-full text-xs font-medium border-2" style="background-color: {{ $topic->category->background_color }}; border-color: {{ $topic->category->border_color }};">
                                {{ $topic->category->name }}
                            </div>
                            @else
                            <span class="text-gray-400">Không có danh mục</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">{{ $topic->sort_order }}</td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            @if($topic->featured)
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                <i class="fas fa-star mr-1"></i> Nổi bật
                            </span>
                            @else
                            <span class="text-gray-400">-</span>
                            @endif
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $topic->status == 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $topic->status == 'active' ? 'Hoạt động' : 'Không hoạt động' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                            <a href="{{ route('admin.medical-topics.edit', $topic->id) }}" class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('admin.medical-topics.delete', $topic->id) }}" method="POST" class="inline-block">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Bạn có chắc chắn muốn xóa chủ đề này?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-4 py-3 text-center text-gray-500">Không có chủ đề y tế nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {{ $topics->links() }}
        </div>
    </div>
</div>
@endsection
