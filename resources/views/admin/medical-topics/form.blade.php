@extends('admin.layouts.app')

@section('title', isset($topic) ? 'Chỉnh sửa chủ đề y tế' : 'Thêm chủ đề y tế mới')
@section('header', isset($topic) ? 'Chỉnh sửa chủ đề y tế' : 'Thêm chủ đề y tế mới')

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ isset($topic) ? 'Chỉnh sửa chủ đề y tế' : 'Thêm chủ đề y tế mới' }}</h3>
    </div>
    <form action="{{ isset($topic) ? route('admin.medical-topics.update', $topic->id) : route('admin.medical-topics.store') }}" method="POST">
        @csrf
        @if(isset($topic))
            @method('PUT')
        @endif
        
        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Tiêu đề <span class="text-red-500">*</span></label>
                    <input type="text" name="title" id="title" value="{{ old('title', isset($topic) ? $topic->title : '') }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                    <input type="text" name="slug" id="slug" value="{{ old('slug', isset($topic) ? $topic->slug : '') }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Để trống để tự động tạo">
                    @error('slug')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-1">Danh mục <span class="text-red-500">*</span></label>
                    <select name="category_id" id="category_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" required>
                        <option value="">Chọn danh mục</option>
                        @foreach($categories as $category)
                        <option value="{{ $category->id }}" 
                            {{ old('category_id', isset($topic) ? $topic->category_id : '') == $category->id ? 'selected' : '' }}
                            data-bg-color="{{ $category->background_color }}" 
                            data-border-color="{{ $category->border_color }}">
                            {{ $category->name }}
                        </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Trạng thái <span class="text-red-500">*</span></label>
                    <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" required>
                        <option value="active" {{ old('status', isset($topic) ? $topic->status : 'active') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                        <option value="inactive" {{ old('status', isset($topic) ? $topic->status : '') == 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">Thứ tự sắp xếp</label>
                    <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', isset($topic) ? $topic->sort_order : 0) }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" min="0">
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="featured" id="featured" value="1" {{ old('featured', isset($topic) && $topic->featured ? 'checked' : '') }} class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <label for="featured" class="ml-2 block text-sm text-gray-900">
                        Chủ đề nổi bật
                    </label>
                    @error('featured')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Mô tả ngắn</label>
                <textarea name="description" id="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Mô tả ngắn về chủ đề...">{{ old('description', isset($topic) ? $topic->description : '') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Nội dung chi tiết</label>
                <textarea name="content" id="content" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary" placeholder="Nội dung chi tiết về chủ đề...">{{ old('content', isset($topic) ? $topic->content : '') }}</textarea>
                @error('content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            
            <!-- Preview -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Xem trước</label>
                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div id="category-preview" class="inline-block px-3 py-1 rounded-full text-xs font-medium border-2 mb-2" style="display: none;">
                        <span id="preview-category-text"></span>
                    </div>
                    <h3 id="preview-title" class="text-lg font-medium text-gray-900 mb-2">{{ old('title', isset($topic) ? $topic->title : 'Tiêu đề chủ đề') }}</h3>
                    <p id="preview-description" class="text-sm text-gray-600">{{ old('description', isset($topic) ? $topic->description : 'Mô tả ngắn về chủ đề...') }}</p>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <a href="{{ route('admin.medical-topics') }}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                Hủy
            </a>
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                {{ isset($topic) ? 'Cập nhật' : 'Tạo mới' }}
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');
    const descriptionInput = document.getElementById('description');
    const categorySelect = document.getElementById('category_id');
    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const categoryPreview = document.getElementById('category-preview');
    const previewCategoryText = document.getElementById('preview-category-text');
    
    // Auto-generate slug from title
    titleInput.addEventListener('input', function() {
        if (!slugInput.value || slugInput.value === slugify(titleInput.dataset.oldValue || '')) {
            slugInput.value = slugify(this.value);
        }
        titleInput.dataset.oldValue = this.value;
        updatePreview();
    });
    
    // Update description preview
    descriptionInput.addEventListener('input', updatePreview);
    
    // Update category preview
    categorySelect.addEventListener('change', updatePreview);
    
    // Update preview
    function updatePreview() {
        previewTitle.textContent = titleInput.value || 'Tiêu đề chủ đề';
        previewDescription.textContent = descriptionInput.value || 'Mô tả ngắn về chủ đề...';
        
        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
        if (selectedOption && selectedOption.value) {
            const bgColor = selectedOption.dataset.bgColor;
            const borderColor = selectedOption.dataset.borderColor;
            categoryPreview.style.backgroundColor = bgColor;
            categoryPreview.style.borderColor = borderColor;
            previewCategoryText.textContent = selectedOption.text;
            categoryPreview.style.display = 'inline-block';
        } else {
            categoryPreview.style.display = 'none';
        }
    }
    
    // Slugify function
    function slugify(text) {
        return text
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/[đĐ]/g, 'd')
            .replace(/[^a-z0-9\s-]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-');
    }
    
    // Initialize preview
    updatePreview();
});
</script>
@endsection
