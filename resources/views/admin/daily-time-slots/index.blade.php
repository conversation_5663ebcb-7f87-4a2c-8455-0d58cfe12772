@extends('admin.layouts.app')

@section('header', '<PERSON><PERSON><PERSON><PERSON>')

@section('content')
<style>
    .time-slot-btn {
        font-size: 11px;
        padding: 4px 6px;
        margin: 1px;
        border: 1px solid #dee2e6;
        background-color: #f8f9fa;
        color: #495057;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .time-slot-btn.active {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    .time-slot-btn:hover {
        background-color: #e9ecef;
    }

    .time-slot-btn.active:hover {
        background-color: #218838;
    }

    .date-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #fff;
    }

    .date-card.today {
        border-color: #007bff;
        background-color: #f8f9ff;
    }

    .date-card.sunday {
        border-color: #dc3545;
        background-color: #fff5f5;
    }

    .date-card.sunday .date-header {
        color: #dc3545;
    }

    .date-header {
        font-weight: bold;
        margin-bottom: 8px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
        font-size: 14px;
    }

    .time-slots-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
        gap: 2px;
    }

    .bulk-actions {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .clone-week-section {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        color: white;
    }

    .clone-week-section .form-control {
        background-color: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #333;
    }

    .clone-week-section .form-control:focus {
        background-color: white;
        border-color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }

    .dates-container {
        max-height: 70vh;
        overflow-y: auto;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Cài Đặt Khung Giờ Khám - 30 Ngày Tới</h3>
                </div>

                <div class="card-body">
                    @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                    @endif

                    <!-- Bulk Actions -->
                    <div class="bulk-actions">
                        <h5 class="mb-3">
                            <i class="fas fa-magic mr-2"></i>
                            Cài Đặt Nhanh
                        </h5>

                        <form action="{{ route('admin.daily-time-slots.bulk-update') }}" method="POST" id="bulk-form">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="apply_to_all" value="1">

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="text-white mb-2">Chọn khung giờ áp dụng cho tất cả 30 ngày:</label>
                                    <div class="time-slots-grid">
                                        @foreach($allTimeSlots as $timeSlot)
                                        <button type="button" class="time-slot-btn bulk-slot"
                                            data-time="{{ $timeSlot }}">
                                            {{ $timeSlot }}
                                        </button>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-light btn-sm" id="select-morning">
                                        <i class="fas fa-sun"></i> Sáng (7:00-12:00)
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-light btn-sm" id="select-afternoon">
                                        <i class="fas fa-cloud-sun"></i> Chiều (13:00-17:00)
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-light btn-sm" id="select-evening">
                                        <i class="fas fa-moon"></i> Tối (18:00-22:00)
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-save"></i> Áp Dụng Tất Cả
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Clone Week Section -->
                    <div class="clone-week-section">
                        <h5 class="mb-3">
                            <i class="fas fa-copy mr-2"></i>
                            Sao Chép Lịch 2 Tuần
                        </h5>

                        <div class="mb-3">
                            <p class="text-white mb-2">
                                <i class="fas fa-info-circle mr-2"></i>
                                Chức năng này sẽ tự động sao chép lịch của 2 tuần gần đây (tuần trước + tuần hiện tại)
                                cho 2 tuần tiếp theo.
                            </p>
                            <small class="text-muted">
                                Ví dụ: Hôm nay là {{ date('d/m/Y') }}, hệ thống sẽ sao chép lịch của 2 tuần vừa qua
                                cho 2 tuần tới.
                            </small>
                        </div>

                        <form action="{{ route('admin.daily-time-slots.clone-week') }}" method="POST" id="clone-form">
                            @csrf
                            <div class="text-center">
                                <button type="submit" class="btn btn-warning btn-lg"
                                    onclick="return confirm('Bạn có chắc chắn muốn sao chép lịch? Điều này sẽ ghi đè lên lịch hiện tại của 2 tuần tới.')">
                                    <i class="fas fa-copy"></i> Sao Chép Lịch 2 Tuần Tới
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Individual Days -->
                    <form action="{{ route('admin.daily-time-slots.update') }}" method="POST" id="main-form">
                        @csrf
                        @method('PUT')

                        <div class="dates-container">
                            <div class="row">
                                @foreach($datesData as $index => $dateData)
                                <div class="col-md-6 col-lg-4 col-xl-3">
                                    <div
                                        class="date-card {{ $dateData['is_today'] ? 'today' : '' }} {{ $dateData['day_name'] == 'CN' ? 'sunday' : '' }}">
                                        <div class="date-header">
                                            {{ $dateData['date_formatted'] }} - {{ $dateData['day_name'] }}
                                            @if($dateData['is_today'])
                                            <span class="badge badge-primary">Hôm nay</span>
                                            @endif
                                            @if($dateData['day_name'] == 'CN')
                                            <span class="badge badge-danger">Chủ nhật</span>
                                            @endif
                                        </div>

                                        <input type="hidden" name="slots[{{ $index }}][date]"
                                            value="{{ $dateData['date'] }}">

                                        <div class="time-slots-grid">
                                            @foreach($dateData['time_slots'] as $slot)
                                            <button type="button"
                                                class="time-slot-btn {{ $slot['is_available'] ? 'active' : '' }}"
                                                data-date="{{ $dateData['date'] }}" data-time="{{ $slot['time_slot'] }}"
                                                data-index="{{ $index }}">
                                                {{ $slot['time_slot'] }}
                                            </button>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> Lưu Tất Cả Thay Đổi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Handle individual time slot clicks
    document.querySelectorAll('.time-slot-btn:not(.bulk-slot)').forEach(btn => {
        btn.addEventListener('click', function() {
            this.classList.toggle('active');
            updateHiddenInputs();
        });
    });

    // Handle bulk time slot clicks
    document.querySelectorAll('.bulk-slot').forEach(btn => {
        btn.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    });

    // Quick select buttons for bulk
    document.getElementById('select-morning').addEventListener('click', function() {
        selectBulkTimeRange('07:00', '12:00');
    });

    document.getElementById('select-afternoon').addEventListener('click', function() {
        selectBulkTimeRange('13:00', '17:00');
    });

    document.getElementById('select-evening').addEventListener('click', function() {
        selectBulkTimeRange('18:00', '22:00');
    });

    function selectBulkTimeRange(startTime, endTime) {
        document.querySelectorAll('.bulk-slot').forEach(btn => {
            const timeValue = btn.dataset.time;
            if (timeValue >= startTime && timeValue <= endTime) {
                btn.classList.add('active');
            }
        });
    }

    // Update hidden inputs for main form
    function updateHiddenInputs() {
        // Remove existing hidden inputs
        document.querySelectorAll('input[name*="time_slots"]').forEach(input => {
            input.remove();
        });

        // Add new hidden inputs based on active buttons
        document.querySelectorAll('.date-card').forEach((card, dateIndex) => {
            const activeSlots = card.querySelectorAll('.time-slot-btn.active');
            activeSlots.forEach((btn, slotIndex) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = `slots[${dateIndex}][time_slots][]`;
                input.value = btn.dataset.time;
                card.appendChild(input);
            });
        });
    }

    // Handle bulk form submission
    document.getElementById('bulk-form').addEventListener('submit', function(e) {
        const activeSlots = document.querySelectorAll('.bulk-slot.active');
        if (activeSlots.length === 0) {
            e.preventDefault();
            alert('Vui lòng chọn ít nhất một khung giờ!');
            return;
        }

        // Add selected time slots to form
        activeSlots.forEach(btn => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'time_slots[]';
            input.value = btn.dataset.time;
            this.appendChild(input);
        });
    });

    // Initialize hidden inputs
    updateHiddenInputs();
});
</script>
@endsection