<x-app-layout>
    <div class="container mx-auto py-8">
        <h1 class="text-2xl font-bold mb-4">Test Data</h1>
        
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Categories ({{ $globalCategories->count() }})</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($globalCategories as $category)
                <div class="p-4 border rounded-lg">
                    <div class="flex items-center mb-2">
                        <span class="inline-block w-4 h-4 rounded-full mr-2" 
                            style="background-color: {{ $category->background_color }}; border: 1px solid {{ $category->border_color }};"></span>
                        <h3 class="font-semibold">{{ $category->name }}</h3>
                    </div>
                    <p class="text-sm text-gray-600">{{ $category->description }}</p>
                    <p class="text-xs text-gray-500 mt-2">Sort: {{ $category->sort_order }}</p>
                </div>
                @endforeach
            </div>
        </div>
        
        <div class="mb-8">
            <h2 class="text-xl font-semibold mb-4">Featured Medical Topics ({{ $globalFeaturedMedicalTopics->count() }})</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($globalFeaturedMedicalTopics as $topic)
                <div class="p-4 border rounded-lg">
                    <div class="flex items-center mb-2">
                        @if($topic->category)
                        <span class="inline-block px-2 py-1 text-xs rounded mr-2" 
                            style="background-color: {{ $topic->category->background_color }}; border: 1px solid {{ $topic->category->border_color }}; color: {{ $topic->category->border_color }};">
                            {{ $topic->category->name }}
                        </span>
                        @endif
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 text-xs rounded">Featured</span>
                    </div>
                    <h3 class="font-semibold mb-2">{{ $topic->title }}</h3>
                    <p class="text-sm text-gray-600">{{ $topic->description }}</p>
                    <p class="text-xs text-gray-500 mt-2">Sort: {{ $topic->sort_order }}</p>
                </div>
                @endforeach
            </div>
        </div>
        
        <div>
            <h2 class="text-xl font-semibold mb-4">All Medical Topics ({{ $globalMedicalTopics->count() }})</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($globalMedicalTopics as $topic)
                <div class="p-4 border rounded-lg">
                    <div class="flex items-center mb-2">
                        @if($topic->category)
                        <span class="inline-block px-2 py-1 text-xs rounded mr-2" 
                            style="background-color: {{ $topic->category->background_color }}; border: 1px solid {{ $topic->category->border_color }}; color: {{ $topic->category->border_color }};">
                            {{ $topic->category->name }}
                        </span>
                        @endif
                        @if($topic->featured)
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 text-xs rounded">Featured</span>
                        @endif
                    </div>
                    <h3 class="font-semibold mb-2">{{ $topic->title }}</h3>
                    <p class="text-sm text-gray-600">{{ Str::limit($topic->description, 100) }}</p>
                    <p class="text-xs text-gray-500 mt-2">Sort: {{ $topic->sort_order }} | Status: {{ $topic->status }}</p>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</x-app-layout>
