<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Test</title>
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-8">Video Player Test</h1>
        
        <!-- Test Video Player -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">Direct Video Player</h2>
            <div id="videoPlayer" data-plyr-provider="youtube" data-plyr-embed-id="dQw4w9WgXcQ" class="rounded-lg overflow-hidden"></div>
        </div>
        
        <!-- Test Video Modal Trigger -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">Video Modal Test</h2>
            <button onclick="openVideoModal('https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'Test Video', '22/06/2025', '1 hour ago', 'This is a test video excerpt')" 
                    class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Open Video Modal
            </button>
        </div>
        
        <!-- Debug Info -->
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Debug Info</h2>
            <div id="debugInfo" class="text-sm text-gray-600"></div>
        </div>
    </div>

    <!-- Video Modal -->
    <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden overflow-y-auto p-4">
        <div class="bg-white rounded-lg max-w-5xl w-full my-8 relative max-h-[calc(100vh-4rem)] overflow-hidden flex flex-col">
            <!-- Header -->
            <div class="flex justify-between items-center p-4 border-b bg-white flex-shrink-0">
                <h3 id="videoTitle" class="text-lg font-semibold text-gray-900 pr-4"></h3>
                <button onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Scrollable Content -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-6">
                    <!-- Video Player -->
                    <div class="relative mb-6">
                        <div id="videoPlayerModal" data-plyr-provider="youtube" data-plyr-embed-id=""></div>
                    </div>
                    
                    <!-- Video Info -->
                    <div id="videoInfo" class="space-y-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <span id="videoDate"></span>
                            <span class="mx-2 text-orange-400 font-bold">•</span>
                            <span id="videoReadTime"></span>
                        </div>
                        <div id="videoExcerpt" class="text-gray-700 leading-relaxed"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
    <script>
        let player = null;
        let modalPlayer = null;
        
        function log(message, data = null) {
            console.log(message, data);
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `<div>[${timestamp}] ${message} ${data ? JSON.stringify(data) : ''}</div>`;
        }
        
        function openVideoModal(youtubeLink, title, date, timeAgo, excerpt) {
            log('Opening video modal with:', { youtubeLink, title, date, timeAgo, excerpt });
            
            const modal = document.getElementById('videoModal');
            const videoPlayerModal = document.getElementById('videoPlayerModal');
            const videoTitle = document.getElementById('videoTitle');
            const videoDate = document.getElementById('videoDate');
            const videoReadTime = document.getElementById('videoReadTime');
            const videoExcerpt = document.getElementById('videoExcerpt');
            
            log('Modal elements found:', { 
                modal: !!modal, 
                videoPlayerModal: !!videoPlayerModal, 
                videoTitle: !!videoTitle 
            });
            
            // Extract video ID from YouTube URL
            let videoId = '';
            if (youtubeLink.includes('youtube.com/watch?v=')) {
                videoId = youtubeLink.split('v=')[1].split('&')[0];
            } else if (youtubeLink.includes('youtu.be/')) {
                videoId = youtubeLink.split('youtu.be/')[1].split('?')[0];
            }
            
            log('Extracted video ID:', videoId);
            
            if (videoId) {
                // Set video info
                videoTitle.textContent = title;
                videoDate.textContent = date;
                videoReadTime.textContent = timeAgo;
                videoExcerpt.textContent = excerpt;
                
                // Set video ID for Plyr
                videoPlayerModal.setAttribute('data-plyr-embed-id', videoId);
                
                // Initialize Plyr
                if (modalPlayer) {
                    modalPlayer.destroy();
                }
                
                try {
                    modalPlayer = new Plyr('#videoPlayerModal', {
                        controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
                        youtube: {
                            noCookie: false,
                            rel: 0,
                            showinfo: 0,
                            iv_load_policy: 3,
                            modestbranding: 1
                        }
                    });
                    log('Modal player initialized successfully');
                } catch (error) {
                    log('Error initializing modal player:', error.message);
                }
                
                modal.classList.remove('hidden');
                modal.classList.add('flex');
                document.body.style.overflow = 'hidden';
                
                log('Modal opened');
            } else {
                log('No video ID extracted from URL:', youtubeLink);
            }
        }
        
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            
            if (modalPlayer) {
                modalPlayer.destroy();
                modalPlayer = null;
            }
            
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.body.style.overflow = 'auto';
            
            log('Modal closed');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, initializing main video player');
            
            // Initialize main video player
            const mainVideoPlayer = document.getElementById('videoPlayer');
            if (mainVideoPlayer) {
                const videoId = mainVideoPlayer.getAttribute('data-plyr-embed-id');
                log('Main video player found with ID:', videoId);
                
                if (videoId) {
                    try {
                        player = new Plyr('#videoPlayer', {
                            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
                            youtube: {
                                noCookie: false,
                                rel: 0,
                                showinfo: 0,
                                iv_load_policy: 3,
                                modestbranding: 1
                            }
                        });
                        log('Main video player initialized successfully');
                    } catch (error) {
                        log('Error initializing main player:', error.message);
                    }
                } else {
                    log('No video ID found for main player');
                }
            } else {
                log('No main video player element found');
            }
        });
        
        // Close modal when clicking outside
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideoModal();
            }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeVideoModal();
            }
        });
    </script>
</body>
</html>
