<!-- Appointment Booking Section -->
<section id="appointment" class="py-10 px-0 lg:px-4">
    <div class="container mx-auto">
        <div class="bg-formBlue max-w-5xl mx-auto rounded-3xl">
            <!-- Modern Appointment Card -->
            <div class="rounded-3xl p-3 lg:p-8 shadow-2xl">
                <h2 class="text-3xl font-bold text-white text-center mb-4 lg:mb-8">Đặt Lịch Hẹn</h2>

                <!-- Appointment Form -->
                <div class="bg-white rounded-2xl p-6">
                    <form x-data="appointmentForm()" x-init="init()" @submit.prevent="submitForm" class="space-y-6">
                        <!-- Row 1: Name, Year, Phone -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Họ và tên <span
                                        class="text-red-500">*</span></label>
                                <input type="text" x-model="form.name" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Nhập họ và tên">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Năm sinh <span
                                        class="text-red-500">*</span></label>
                                <div class="relative" x-data="{ showYearPicker: false }">
                                    <input type="text" x-model="form.birthYear" required @focus="showYearPicker = true"
                                        @input="validateYear($event)"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                                        placeholder="Nhập hoặc chọn năm sinh" maxlength="4" pattern="[0-9]{4}">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                                        @click="showYearPicker = !showYearPicker">
                                        <svg class="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                            </path>
                                        </svg>
                                    </div>

                                    <!-- Year Picker Dropdown -->
                                    <div x-show="showYearPicker" x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 scale-95"
                                        x-transition:enter-end="opacity-100 scale-100"
                                        x-transition:leave="transition ease-in duration-75"
                                        x-transition:leave-start="opacity-100 scale-100"
                                        x-transition:leave-end="opacity-0 scale-95" @click.away="showYearPicker = false"
                                        @keydown.escape="showYearPicker = false"
                                        class="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                        <div class="p-2">
                                            <div class="text-xs text-gray-500 mb-2 px-2">Chọn năm sinh:</div>
                                            <div class="grid grid-cols-4 gap-1">
                                                <template x-for="year in getYearRange()" :key="year">
                                                    <button type="button"
                                                        @click="form.birthYear = year; showYearPicker = false"
                                                        class="px-3 py-2 text-sm rounded hover:bg-blue-100 focus:bg-blue-100 focus:outline-none transition-colors"
                                                        :class="form.birthYear == year ? 'bg-blue-500 text-white' : 'text-gray-700'"
                                                        x-text="year">
                                                    </button>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Số điện thoại <span
                                        class="text-red-500">*</span></label>
                                <input type="tel" x-model="form.phone" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Nhập số điện thoại">
                            </div>
                        </div>

                        <div class="flex gap-2">
                            <div class="flex items-center">
                                <input type="radio" name="status" id="new" class="w-4 h-4" checked>
                                <label for="new" class="ml-2">Khám lần đầu</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="status" id="returning" class="w-4 h-4">
                                <label for="returning" class="ml-2">Tái khám</label>
                            </div>
                        </div>

                        <!-- Row 2: Date Selection -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <div class="flex justify-between items-center mb-3">
                                    <label class="block text-sm font-medium text-gray-700">Ngày khám <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex space-x-1">
                                        <button type="button" @click="scrollDates(-1)"
                                            class="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="atDateStart">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                        </button>
                                        <button type="button" @click="scrollDates(1)"
                                            class="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="atDateEnd">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div x-ref="dateContainer" @scroll.debounce.100ms="updateDateScrollStatus()"
                                    class="flex space-x-2 overflow-x-auto pb-2"
                                    style="scrollbar-width: none; -ms-overflow-style: none;">
                                    <div x-show="loading" class="flex items-center justify-center w-full py-4">
                                        <svg class="animate-spin h-6 w-6 text-formBlue"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                            </path>
                                        </svg>
                                        <span class="ml-2 text-gray-600">Đang tải...</span>
                                    </div>
                                    <template x-for="(date, index) in availableDates" :key="date.value"
                                        x-show="!loading">
                                        <button type="button"
                                            @click="date.hasAvailableSlots ? selectDate(date.value) : null"
                                            :disabled="!date.hasAvailableSlots"
                                            :class="form.selectedDate === date.value
                                                ? 'bg-formBlue text-white'
                                                : !date.hasAvailableSlots
                                                    ? 'bg-gray-200 text-[#777E90] cursor-not-allowed'
                                                    : 'bg-white border border-[#E2E8F0] text-textPrimary hover:bg-gray-200'"
                                            class="focus:outline-none flex-shrink-0 px-2 py-1 rounded-xl transition-colors duration-200 text-center min-h-[50px] min-w-[80px] relative">
                                            <div class="text-sm" x-text="date.day"></div>
                                            <div class="text-sm font-medium" x-text="date.date"></div>

                                        </button>
                                    </template>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center">
                                    <label class="block text-sm font-medium text-gray-700">Giờ khám <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex space-x-1">
                                        <button type="button" @click="scrollTimes(-1)"
                                            class="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="atTimeStart">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                        </button>
                                        <button type="button" @click="scrollTimes(1)"
                                            class="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                                            :disabled="atTimeEnd">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div x-ref="timeContainer" @scroll.debounce.100ms="updateTimeScrollStatus()"
                                    class="flex space-x-2 overflow-x-auto pb-2 pt-3"
                                    style="scrollbar-width: none; -ms-overflow-style: none;">
                                    <template x-for="time in availableTimes" :key="time.value">
                                        <button type="button" @click="selectTime(time.value)"
                                            :class="form.selectedTime === time.value ? 'bg-formBlue text-white' : time.available ? 'bg-bgTime text-textTime' : 'bg-gray-50 text-gray-400 cursor-not-allowed'"
                                            :disabled="!time.available"
                                            class="min-w-[80px] relative px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none flex-shrink-0 min-h-[50px] flex items-center justify-center"
                                            style="min-width: 80px;">
                                            <span x-text="time.label"></span>
                                            <div x-show="!time.available"
                                                class="absolute -right-1 -top-2 bg-red-500 text-white text-[10px] font-semibold p-0 rounded-full uppercase h-[17px] w-[32px] flex items-center justify-center">
                                                Full
                                            </div>
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <!-- Notes Section -->
                        <div>
                            <textarea x-model="form.notes" rows="3"
                                class="w-full px-4 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none bg-[#F5F5F5]"
                                placeholder="Mô tả tình trạng sức khỏe"></textarea>
                        </div>

                        <!-- Important Notice -->
                        <div class="note mt-1" style="margin-top: 10px;">
                            <p class="text-sm text-[#E28C00] italic">
                                Thực tế giờ khám có thể chậm hơn một chút. Mong quý khách thông cảm. Trường hợp quý
                                khách muốn đăng ký khung giờ khác, vui lòng gọi điện thoại số 0981705467.
                            </p>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-center">
                            <button type="submit" :disabled="loading"
                                class="w-fit bg-formBlue px-5 self-center text-white py-4 rounded-lg font-semibold text-[16px] hover:bg-blue-700 transition duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed">
                                <span x-show="!loading">Đặt hẹn ngay</span>
                                <span x-show="loading" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                            stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                        </path>
                                    </svg>
                                    Đang xử lý...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    function appointmentForm() {
    return {
        form: {
            name: '',
            birthYear: '',
            phone: '',
            selectedDate: '',
            selectedTime: '',
            notes: ''
        },
        atDateStart: true,
        atDateEnd: false,
        atTimeStart: true,
        atTimeEnd: false,
        async init() {
            await this.loadAvailableSlots();
            this.$nextTick(() => {
                this.updateDateScrollStatus();
                this.updateTimeScrollStatus();
            })
        },
        async loadAvailableSlots() {
            this.loading = true;
            try {
                const response = await fetch('/api/appointments/available-slots');
                const data = await response.json();

                if (data.success) {
                    this.availableDates = data.data.dates;
                    // Set times for first date if available
                    if (this.availableDates.length > 0) {
                        this.availableTimes = this.availableDates[0].times;
                    }
                }
            } catch (error) {
                console.error('Error loading available slots:', error);
            } finally {
                this.loading = false;
            }
        },
        updateDateScrollStatus() {
            const el = this.$refs.dateContainer;
            if (!el) return;
            this.atDateStart = el.scrollLeft <= 0;
            this.atDateEnd = el.scrollWidth - el.scrollLeft - el.clientWidth <= 1;
        },
        updateTimeScrollStatus() {
            const el = this.$refs.timeContainer;
            if (!el) return;

            this.atTimeStart = el.scrollLeft <= 0;
            this.atTimeEnd = el.scrollWidth - el.scrollLeft - el.clientWidth <= 1;

            // If content doesn't overflow, disable both buttons
            if (el.scrollWidth <= el.clientWidth) {
                this.atTimeStart = true;
                this.atTimeEnd = true;
            }
        },
        availableDates: [],
        availableTimes: [],
        loading: false,
        getYearRange() {
            const currentYear = new Date().getFullYear();
            const years = [];
            // Generate years from 1924 to current year (100 years range)
            for (let year = currentYear; year >= currentYear - 100; year--) {
                years.push(year);
            }
            return years;
        },
        validateYear(event) {
            const value = event.target.value;
            const currentYear = new Date().getFullYear();

            // Only allow numbers
            const numericValue = value.replace(/[^0-9]/g, '');

            // Limit to 4 digits
            if (numericValue.length <= 4) {
                this.form.birthYear = numericValue;

                // Hide dropdown when user starts typing
                if (event.inputType && event.inputType.includes('insert')) {
                    this.$nextTick(() => {
                        this.showYearPicker = false;
                    });
                }

                // Validate year range if 4 digits entered
                if (numericValue.length === 4) {
                    const year = parseInt(numericValue);
                    if (year < currentYear - 100 || year > currentYear) {
                        // Invalid year range - could show warning
                        console.warn('Năm sinh không hợp lệ');
                    }
                }
            }
        },
        scrollDates(direction) {
            this.$refs.dateContainer.scrollBy({ left: direction * 200, behavior: 'smooth' });
        },
        scrollTimes(direction) {
            this.$refs.timeContainer.scrollBy({ left: direction * 200, behavior: 'smooth' });
            // Update scroll status after scrolling
            setTimeout(() => {
                this.updateTimeScrollStatus();
            }, 300);
        },
        selectDate(date) {
            // Find the selected date data
            const selectedDateData = this.availableDates.find(d => d.value === date);

            // Only allow selection if the date has available slots
            if (!selectedDateData || !selectedDateData.hasAvailableSlots) {
                return;
            }

            this.form.selectedDate = date;
            this.form.selectedTime = ''; // Reset selected time

            // Update available times
            if (selectedDateData) {
                this.availableTimes = selectedDateData.times;
            }

            // Update time scroll status after times are updated
            this.$nextTick(() => {
                this.updateTimeScrollStatus();
            });
        },
        selectTime(time) {
            this.form.selectedTime = time;
        },
        async submitForm() {
            // Validate required fields
            if (!this.form.name || !this.form.birthYear || !this.form.phone || !this.form.selectedDate || !this.form.selectedTime) {
                showWarning('Vui lòng điền đầy đủ thông tin bắt buộc');
                return;
            }

            // Validate and convert birth year
            const birthYear = String(this.form.birthYear).trim();
            if (!/^\d{4}$/.test(birthYear)) {
                showWarning('Năm sinh phải là 4 chữ số');
                return;
            }

            const currentYear = new Date().getFullYear();
            const yearNum = parseInt(birthYear);
            if (yearNum < currentYear - 100 || yearNum > currentYear) {
                showWarning('Năm sinh không hợp lệ');
                return;
            }

            this.loading = true;

            try {
                const formData = {
                    name: String(this.form.name).trim(),
                    birth_year: birthYear,
                    phone: String(this.form.phone).trim(),
                    appointment_date: this.form.selectedDate,
                    appointment_time: this.form.selectedTime,
                    notes: this.form.notes ? String(this.form.notes).trim() : '',
                    status_type: document.querySelector('input[name="status"]:checked')?.id || 'new'
                };

                console.log('Submitting appointment:', formData);

                const response = await fetch('/api/appointments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('API Error:', errorData);
                    showError(errorData.message || 'Có lỗi xảy ra. Vui lòng thử lại.');
                    return;
                }

                const data = await response.json();
                console.log('API Response:', data);

                if (data.success) {
                    showSuccess(data.message);

                    // Reset form
                    this.form = {
                        name: '',
                        birthYear: '',
                        phone: '',
                        selectedDate: '',
                        selectedTime: '',
                        notes: ''
                    };

                    // Reset radio buttons
                    document.getElementById('new').checked = true;

                    // Reload available slots to update availability
                    await this.loadAvailableSlots();
                } else {
                    showError(data.message || 'Có lỗi xảy ra. Vui lòng thử lại.');
                }
            } catch (error) {
                console.error('Error submitting appointment:', error);
                showError('Có lỗi xảy ra. Vui lòng thử lại.');
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>
@endpush