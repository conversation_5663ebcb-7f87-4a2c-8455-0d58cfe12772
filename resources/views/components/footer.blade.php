<!-- FOOTER SECTION -->
<div x-data="{ showPhone: false}">

    <footer class="py-16 border-t-[4px] border-[#004B7F] bg-white">
        <div class="container mx-auto px-4 max-w-5xl">
            <!-- Header Section -->
            <div class="text-center mb-12">
                <h2 class="text-2xl font-bold text-blue-600 mb-4 uppercase">Tiến sĩ - <PERSON><PERSON>c sĩ lý đại lương</h2>
                <p class="text-gray-600 text-sm max-w-4xl mx-auto leading-relaxed">
                    Phòng khám được kiến trúc sư thiết kế theo phong cách ấm áp, gần gũi, thể hiện tinh thần tận tâm
                    chăm
                    sóc người bệnh. Phòng khám được trang bị cân TANITA đo thành phần cơ thể, máy đo huyết áp tự động
                    đượ<PERSON>
                    nhập khẩu từ <PERSON>, m<PERSON><PERSON> đo chỉ số huyết áp động mạch cổ chân - c<PERSON>h tay (ABI), máy đo
                    ceton
                    máu tại giường, monofilament 10g và các mô hình thực phẩm để tư vấn dinh dưỡng. Chúng tôi hân hạnh
                    được
                    chào đón và phục vụ quý khách!
                </p>
            </div>

            <!-- Specialties Tags -->
            <div class="flex flex-wrap justify-center gap-2 mb-8 max-w-full lg:max-w-4xl">
                @foreach($globalCategories as $category)
                <span class="text-xs lg:text-base uppercase border px-4 py-1 rounded-[6px] font-medium text-textPrimary 
            bg-[{{ $category->background_color }}] border-[{{ $category->border_color }}]"
                    style="background-color: {{ $category->background_color }}; border-color: {{ $category->border_color }};">
                    {{ $category->name }}
                </span>
                @endforeach
            </div>

            <!-- Clinic Photos -->
            <div class="grid grid-cols-1 md:grid-cols-1 gap-6 mb-12">
                <img src="{{ asset('/images/footer/footer.jpg') }}" alt="Khu vực lễ tân"
                    class="hidden lg:block rounded-xl w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                <img src="{{ asset('/images/footer/footer-mobile.jpg') }}" alt="Khu vực lễ tân"
                    class="lg:hidden rounded-xl w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                <!-- Reception Area -->
                {{-- <div class="relative overflow-hidden rounded-2xl shadow-lg min-h-[490px]">
                    <img src="{{ asset('/images/footer/1.jpg') }}" alt="Khu vực lễ tân"
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                </div>

                <!-- Corridor -->
                <div class="relative overflow-hidden rounded-2xl shadow-lg min-h-[490px]">
                    <img src="{{ asset('/images/footer/2.jpg') }}" alt="Hành lang phòng khám"
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                </div>

                <!-- Clinic Exterior and Interior -->
                <div class="space-y-3">
                    <!-- Exterior -->
                    <div class="relative overflow-hidden rounded-2xl shadow-lg min-h-[240px]">
                        <img src="{{ asset('/images/footer/3.jpg') }}" alt="Mặt tiền phòng khám"
                            class="w-full h-full min-h-[240px] object-cover hover:scale-105 transition-transform duration-300">
                    </div>

                    <!-- Interior -->
                    <div class="relative overflow-hidden rounded-2xl shadow-lg min-h-[240px]">
                        <img src="{{ asset('/images/footer/4.jpg') }}" alt="Phòng khám"
                            class="w-full h-full min-h-[240px] object-cover hover:scale-105 transition-transform duration-300">
                    </div>
                </div> --}}
            </div>

            <!-- Contact Information -->
            <div class="text-center mb-8">
                <h3 class="text-xl font-bold text-gray-800 mb-6">Phòng khám Nội tiết BS Lương</h3>

                <div class="space-y-4 max-w-lg mx-auto">
                    <!-- Phone -->
                    <div class="flex items-center justify-center">
                        <i class="fas fa-phone text-blue-600 mr-3"></i>
                        <span class="text-textPrimary">Tel/Zalo: <a href="tel:0981705467"
                                @click="if(window.innerWidth >= 1024) { $event.preventDefault(); showPhone = true; }"
                                class="text-base text-blue-600 transition font-semibold">0981
                                705 467</a></span>
                    </div>

                    <!-- Email -->
                    <div class="flex items-center justify-center">
                        <i class="fas fa-envelope text-blue-600 mr-3"></i>
                        <a href="mailto:<EMAIL>"
                            class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                            <span class="text-textPrimary">Email: </span><EMAIL>
                        </a>
                    </div>

                    <!-- Address -->
                    <div class="flex items-start justify-center">
                        <i class="fas fa-map-marker-alt text-blue-600 mr-3 mt-1"></i>
                        <a href="https://maps.google.com" target="_blank"
                            class="text-blue-600 hover:text-blue-800 transition-colors duration-200 text-left">
                            Địa chỉ: 20C Đường Phó Cơ Điều, Phường 12, Quận 5, Tp.HCM
                        </a>
                    </div>
                </div>
            </div>

            <div x-show="showPhone" x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 backdrop-blur-sm"
                @click.away="showPhone = false" style="display: none;">
                <div class="bg-white rounded-xl p-6 shadow-xl text-center max-w-sm mx-4" @click.stop>
                    <div class="text-lg font-bold mb-2 text-gray-800">Số điện thoại phòng khám</div>
                    <div class="text-2xl font-semibold text-blue-700 mb-4 tracking-wider">0981 705 467</div>
                    <div class="flex gap-2 justify-center">
                        <button @click="showPhone = false"
                            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition">Đóng</button>
                        {{-- <a href="tel:0981705467"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">Gọi
                            ngay</a> --}}
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="text-center border-gray-200 pt-8">
                <h4 class="text-lg font-bold text-gray-800 mb-2">Công ty TNHH Dịch Vụ Y Khoa Đại Phước</h4>
                <p class="text-sm text-gray-600">
                    Giấy phép ĐKKD: 0312377530 do Sở Kế hoạch và đầu tư TP Hồ Chí Minh cấp ngày 27 tháng 6 năm 2024
                </p>
            </div>
        </div>
    </footer>

</div>