<!-- CENTER SECTION -->
<!-- Medical Conditions Section -->
<section class="py-10 px-4" x-data="medicalTopicsData()">
    <div class="max-w-5xl mx-auto">
        <h2 class="text-2xl lg:text-4xl font-bold text-blue-900 text-center mb-6"><PERSON><PERSON><PERSON>ế<PERSON></h2>
        <div
            class="flex items-center rounded-full px-4 py-2 mb-6 max-w-xl mx-auto banner-search-bg border-2 border-lightBorderBlue shadow-[0px_10.63px_31.9px_0px_#00000014]">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z" />
            </svg>
            <input type="text" placeholder="T<PERSON><PERSON> kiếm bệnh" id="searchInput" x-model="searchQuery" @input="searchTopics"
                class="flex-1 bg-transparent outline-none text-base border-none border-transparent focus:border-transparent focus:ring-0" />
        </div>
        <div class="flex flex-wrap gap-2 justify-center mb-6">
            @foreach($globalCategories as $category)
            <button @click="filterByCategory('{{ $category->name }}')"
                :class="searchQuery === '{{ $category->name }}' ? 'bg-formBlue text-white' : 'bg-white text-gray-800'"
                class="px-2 py-1 rounded-full text-[12px] lg:text-[14px] font-medium border-none hover:opacity-80 transition-all duration-200 cursor-pointer">
                {{ $category->name }}
            </button>
            @endforeach
        </div>
        <!-- Accordion List -->
        <div class="space-y-4">
            <template x-for="topic in displayedTopics" :key="topic.id">
                <div x-data="{ open: false }" class="bg-white rounded-2xl shadow px-4 py-3 flex flex-col">
                    <div class="flex items-center justify-between cursor-pointer" @click="open = !open">
                        <div class="header-title flex flex-col">
                            <button x-show="topic.category" @click.stop="filterByCategory(topic.category?.name)"
                                class="text-xs lg:text-sm px-3 py-1 rounded-[6px] mr-2 max-w-fit gap-1 border-[1.4px] text-textPrimary hover:opacity-80 transition-opacity cursor-pointer"
                                :style="`background-color: ${topic.category?.background_color}; border-color: ${topic.category?.border_color};`"
                                x-text="topic.category?.name">
                            </button>
                            <span class="font-bold text-gray-800 mt-1" x-text="topic.title"></span>
                        </div>
                        <svg :class="{'rotate-180': open}"
                            class="w-5 h-5 ml-auto transition-transform text-white bg-black rounded-full" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    <div x-show="open" class="mt-2 text-gray-600 text-sm"
                        x-text="topic.description || `Thông tin chi tiết về ${topic.title}`">
                    </div>
                </div>
            </template>
        </div>
        <div class="flex justify-center mt-8" x-show="hasMoreTopics">
            <button @click="loadMoreTopics"
                class="bg-blue-200 text-blue-900 px-8 py-2 rounded-full font-semibold hover:bg-blue-300 transition flex flex-row justify-center items-center">
                Xem thêm
                <svg class="w-5 h-5 ml-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </button>
        </div>
    </div>
</section>

@include('components.appointment-process')

@include('components.appointment-form')

<!-- News Section -->
<section class="py-16 bg-backgroundLight">
    <div class="container mx-auto px-4">
        <div class="max-w-5xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-primary mb-4">Tin Tức</h2>
                <p class="text-secondary text-lg max-w-3xl mx-auto">
                    Cập nhật về các vấn đề liên quan đến sức khỏe trong lĩnh vực y tế tốt nhất
                </p>
            </div>

            <!-- News Grid -->
            <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 lg:gap-7 gap-3 mb-12">
                @if($featuredNews)
                <!-- Featured Article -->
                <article
                    class="p-4 lg:p-5 group col-span-2 flex flex-col md:flex-row bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    @if($featuredNews->type == 'video')
                    <div class="md:w-1/2 h-64 md:h-auto relative cursor-pointer video-trigger"
                        data-video-url="{{ $featuredNews->video_url }}" data-video-title="{{ $featuredNews->title }}"
                        data-video-date="{{ $featuredNews->created_at->format('d/m/Y') }}"
                        data-video-time="{{ $featuredNews->created_at->diffForHumans() }}"
                        data-video-excerpt="{{ $featuredNews->excerpt }}">
                        <img src="{{ asset($featuredNews->thumbnail ?? $featuredNews->featured_image ?? '/images/news/1.jpg') }}"
                            alt="{{ $featuredNews->title }}" class="w-full h-full object-cover rounded-2xl">
                        <div
                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-2xl">
                            <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-[#004B7F] ml-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </div>
                        </div>
                    </div>
                    @else
                    <a href="{{ route('news.show', $featuredNews->slug) }}" class="md:w-1/2 h-64 md:h-auto">
                        <img src="{{ asset($featuredNews->thumbnail ?? $featuredNews->featured_image ?? '/images/news/1.jpg') }}"
                            alt="{{ $featuredNews->title }}" class="w-full h-full object-cover rounded-2xl">
                    </a>
                    @endif
                    <div class="md:w-1/2 px-6 flex flex-col justify-center">
                        <div class="mb-3 mt-3 lg:mt-0">
                            <span
                                class="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-semibold uppercase">
                                NỔI BẬT
                            </span>
                        </div>
                        <h3
                            class="text-2xl font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 line-clamp-3">
                            <a href="{{ route('news.show', $featuredNews->slug) }}">{{ $featuredNews->title }}</a>
                        </h3>
                        <p class="text-secondary text-sm my-4 line-clamp-3 hidden lg:block">
                            {{ $featuredNews->excerpt }}
                        </p>
                        <div class="flex items-center text-xs lg:text-sm text-secondary mt-3 flex-end">
                            <span>{{ $featuredNews->created_at->format('d/m/Y') }}</span>
                            <span class="mx-2 text-[#FFAE99] font-bold">•</span>
                            <span>{{ $featuredNews->created_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </article>
                @endif

                @foreach($otherNews as $article)
                <!-- Article {{ $loop->iteration + 1 }} -->
                <article
                    class="p-3 lg:p-5 group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    @if($article->type == 'video')
                    <div class="relative h-40 cursor-pointer video-trigger" data-video-url="{{ $article->video_url }}"
                        data-video-title="{{ $article->title }}"
                        data-video-date="{{ $article->created_at->format('d/m/Y') }}"
                        data-video-time="{{ $article->created_at->diffForHumans() }}"
                        data-video-excerpt="{{ $article->excerpt }}">
                        <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                            alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                        <div
                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-2xl">
                            <div class="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-[#004B7F] ml-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </div>
                        </div>
                        @if($article->featured)
                        {{-- <span
                            class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                            NỔI BẬT
                        </span> --}}
                        @endif
                    </div>
                    @else
                    <a href="{{ route('news.show', $article->slug) }}" class="block">
                        <div class="relative h-40">
                            <img src="{{ asset($article->thumbnail ?? $article->featured_image ?? '/images/news/2.jpg') }}"
                                alt="{{ $article->title }}" class="w-full h-full object-cover rounded-2xl">
                            @if($article->featured)
                            {{-- <span
                                class="absolute top-3 left-3 px-3 py-1 rounded-full text-xs font-semibold uppercase bg-yellow-400 text-yellow-900">
                                NỔI BẬT
                            </span> --}}
                            @endif
                        </div>
                    </a>
                    @endif
                    <div class="py-6">
                        <div class="flex items-center text-xs lg:text-sm text-secondary mb-3">
                            <span class="">{{ $article->created_at->format('d/m/Y') }}</span>
                            <span class="text-[#FFAE99] font-bold hidden mx-2 lg:block">•</span>
                            <span class="text-[#96B4CA] font-bold mx-[2px] lg:hidden">•</span>
                            <span class="whitespace-nowrap">{{ $article->created_at->diffForHumans() }}</span>
                        </div>
                        <h3
                            class="text-lg font-bold text-textPrimary group-hover:text-primary transition-colors duration-200 mb-3 line-clamp-3">
                            <a href="{{ route('news.show', $article->slug) }}">{{ $article->title }}</a>
                        </h3>
                    </div>
                </article>
                @endforeach
            </div>

            <!-- View All Button -->
            <div class="text-center">
                <a href="{{ route('news.index') }}"
                    class="bg-paleBlue hover:bg-blue-200 text-primary font-semibold px-8 py-3 rounded-full transition-colors duration-200 inline-block">
                    Xem tất cả
                </a>
            </div>
        </div>
    </div>
</section>


<script>
    function medicalTopicsData() {
    return {
        searchQuery: '',
        allTopics: @json($globalMedicalTopics),
        displayedTopics: [],
        currentPage: 0,
        topicsPerPage: 6,
        filteredTopics: [],

        init() {
            this.filteredTopics = this.allTopics;
            this.loadInitialTopics();
        },

        loadInitialTopics() {
            // Load first 6 topics: featured first, then non-featured
            const featured = this.filteredTopics.filter(topic => topic.featured);
            const nonFeatured = this.filteredTopics.filter(topic => !topic.featured);

            const initialTopics = [...featured, ...nonFeatured].slice(0, this.topicsPerPage);
            this.displayedTopics = initialTopics;
            this.currentPage = 1;
        },

        searchTopics() {
            if (this.searchQuery.trim() === '') {
                this.filteredTopics = this.allTopics;
            } else {
                const query = this.searchQuery.toLowerCase();
                this.filteredTopics = this.allTopics.filter(topic =>
                    topic.title.toLowerCase().includes(query) ||
                    (topic.description && topic.description.toLowerCase().includes(query)) ||
                    (topic.content && topic.content.toLowerCase().includes(query)) ||
                    (topic.category && topic.category.name.toLowerCase().includes(query))
                );
            }
            this.loadInitialTopics();
        },

        filterByCategory(categoryName) {
            this.searchQuery = categoryName;
            this.searchTopics();
        },

        loadMoreTopics() {
            const startIndex = this.currentPage * this.topicsPerPage;
            const endIndex = startIndex + this.topicsPerPage;

            // Load next 6 topics: featured first, then non-featured
            const featured = this.filteredTopics.filter(topic => topic.featured);
            const nonFeatured = this.filteredTopics.filter(topic => !topic.featured);
            const sortedTopics = [...featured, ...nonFeatured];

            const nextTopics = sortedTopics.slice(startIndex, endIndex);
            this.displayedTopics = [...this.displayedTopics, ...nextTopics];
            this.currentPage++;
        },

        get hasMoreTopics() {
            const totalDisplayed = this.currentPage * this.topicsPerPage;
            return totalDisplayed < this.filteredTopics.length;
        }
    }
}
</script>

<!-- Video Modal -->
<div id="videoModal"
    class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden overflow-y-auto p-4">
    <div
        class="bg-white rounded-lg max-w-5xl w-full my-8 relative max-h-[calc(100vh-4rem)] overflow-hidden flex flex-col">
        <!-- Header -->
        <div class="flex justify-between items-center p-4 border-b bg-white flex-shrink-0">
            <h3 id="videoTitle" class="text-lg font-semibold text-gray-900 pr-4"></h3>
            <button onclick="closeVideoModal()" class="text-gray-400 hover:text-gray-600 flex-shrink-0">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <!-- Scrollable Content -->
        <div class="flex-1 overflow-y-auto">
            <div class="p-6">
                <!-- Video Player -->
                <div class="relative mb-6">
                    <div id="videoPlayer" data-plyr-provider="youtube" data-plyr-embed-id=""></div>
                </div>

                <!-- Video Info -->
                <div id="videoInfo" class="space-y-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <span id="videoDate"></span>
                        <span class="mx-2 text-[#FFAE99] font-bold">•</span>
                        <span id="videoReadTime"></span>
                    </div>
                    <div id="videoExcerpt" class="text-gray-700 leading-relaxed"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let player = null;

function openVideoModal(youtubeLink, title, date, timeAgo, excerpt) {
    const modal = document.getElementById('videoModal');
    const videoPlayer = document.getElementById('videoPlayer');
    const videoTitle = document.getElementById('videoTitle');
    const videoDate = document.getElementById('videoDate');
    const videoReadTime = document.getElementById('videoReadTime');
    const videoExcerpt = document.getElementById('videoExcerpt');

    // Extract video ID from YouTube URL
    let videoId = '';
    if (youtubeLink.includes('youtube.com/watch?v=')) {
        videoId = youtubeLink.split('v=')[1].split('&')[0];
    } else if (youtubeLink.includes('youtu.be/')) {
        videoId = youtubeLink.split('youtu.be/')[1].split('?')[0];
    }

    if (videoId) {
        // Set video info
        videoTitle.textContent = title;
        videoDate.textContent = date;
        videoReadTime.textContent = timeAgo;
        videoExcerpt.textContent = excerpt;

        // Set video ID for Plyr
        videoPlayer.setAttribute('data-plyr-embed-id', videoId);

        // Initialize Plyr
        if (player) {
            player.destroy();
        }
        player = new Plyr('#videoPlayer', {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            youtube: {
                noCookie: false,
                rel: 0,
                showinfo: 0,
                iv_load_policy: 3,
                modestbranding: 1
            }
        });

        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.style.overflow = 'hidden';
    }
}

// Add event listeners for video triggers
document.addEventListener('DOMContentLoaded', function() {
    const videoTriggers = document.querySelectorAll('.video-trigger');
    videoTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const videoUrl = this.getAttribute('data-video-url');
            const videoTitle = this.getAttribute('data-video-title');
            const videoDate = this.getAttribute('data-video-date');
            const videoTime = this.getAttribute('data-video-time');
            const videoExcerpt = this.getAttribute('data-video-excerpt');

            openVideoModal(videoUrl, videoTitle, videoDate, videoTime, videoExcerpt);
        });
    });
});

function closeVideoModal() {
    const modal = document.getElementById('videoModal');

    if (player) {
        player.destroy();
        player = null;
    }

    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('videoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVideoModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeVideoModal();
    }
});
</script>

<script src="//unpkg.com/alpinejs" defer></script>