<!-- Notification Popup Component -->
<div id="notification-popup" class="fixed inset-0 z-50 hidden">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
    
    <!-- Popup Container -->
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="popup-content">
            <!-- Success Popup -->
            <div id="success-popup" class="hidden">
                <div class="p-6 text-center">
                    <!-- Success Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    
                    <!-- Success Title -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Thành Công!</h3>
                    
                    <!-- Success Message -->
                    <p class="text-gray-600 mb-6" id="success-message">
                        Đặt lịch hẹn thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.
                    </p>
                    
                    <!-- Success Button -->
                    <button onclick="closeNotification()" class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition duration-200">
                        Đóng
                    </button>
                </div>
            </div>
            
            <!-- Error Popup -->
            <div id="error-popup" class="hidden">
                <div class="p-6 text-center">
                    <!-- Error Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    
                    <!-- Error Title -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Có Lỗi Xảy Ra!</h3>
                    
                    <!-- Error Message -->
                    <p class="text-gray-600 mb-6" id="error-message">
                        Có lỗi xảy ra khi đặt lịch hẹn. Vui lòng thử lại.
                    </p>
                    
                    <!-- Error Button -->
                    <button onclick="closeNotification()" class="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition duration-200">
                        Thử Lại
                    </button>
                </div>
            </div>
            
            <!-- Warning Popup -->
            <div id="warning-popup" class="hidden">
                <div class="p-6 text-center">
                    <!-- Warning Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    
                    <!-- Warning Title -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Cảnh Báo!</h3>
                    
                    <!-- Warning Message -->
                    <p class="text-gray-600 mb-6" id="warning-message">
                        Vui lòng điền đầy đủ thông tin bắt buộc.
                    </p>
                    
                    <!-- Warning Button -->
                    <button onclick="closeNotification()" class="w-full bg-yellow-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-yellow-700 transition duration-200">
                        Đã Hiểu
                    </button>
                </div>
            </div>
            
            <!-- Info Popup -->
            <div id="info-popup" class="hidden">
                <div class="p-6 text-center">
                    <!-- Info Icon -->
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    
                    <!-- Info Title -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Thông Tin</h3>
                    
                    <!-- Info Message -->
                    <p class="text-gray-600 mb-6" id="info-message">
                        Thông tin của bạn.
                    </p>
                    
                    <!-- Info Button -->
                    <button onclick="closeNotification()" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition duration-200">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
#notification-popup.show {
    display: flex !important;
}

#notification-popup.show #popup-content {
    transform: scale(1);
    opacity: 1;
}

.notification-enter {
    animation: notificationEnter 0.3s ease-out;
}

.notification-exit {
    animation: notificationExit 0.3s ease-in;
}

@keyframes notificationEnter {
    from {
        transform: scale(0.9) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes notificationExit {
    from {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    to {
        transform: scale(0.9) translateY(-20px);
        opacity: 0;
    }
}
</style>

<script>
// Global notification functions
function showNotification(type, message, title = null) {
    const popup = document.getElementById('notification-popup');
    const content = document.getElementById('popup-content');
    
    // Hide all popup types
    document.getElementById('success-popup').classList.add('hidden');
    document.getElementById('error-popup').classList.add('hidden');
    document.getElementById('warning-popup').classList.add('hidden');
    document.getElementById('info-popup').classList.add('hidden');
    
    // Show the correct popup type
    const targetPopup = document.getElementById(type + '-popup');
    if (targetPopup) {
        targetPopup.classList.remove('hidden');
        
        // Update message
        const messageElement = document.getElementById(type + '-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        // Update title if provided
        if (title) {
            const titleElement = targetPopup.querySelector('h3');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }
    }
    
    // Show popup with animation
    popup.classList.add('show');
    content.classList.add('notification-enter');
    
    // Remove animation class after animation completes
    setTimeout(() => {
        content.classList.remove('notification-enter');
    }, 300);
}

function closeNotification() {
    const popup = document.getElementById('notification-popup');
    const content = document.getElementById('popup-content');
    
    content.classList.add('notification-exit');
    
    setTimeout(() => {
        popup.classList.remove('show');
        content.classList.remove('notification-exit');
    }, 300);
}

// Close on backdrop click
document.getElementById('notification-popup').addEventListener('click', function(e) {
    if (e.target === this) {
        closeNotification();
    }
});

// Close on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeNotification();
    }
});

// Convenience functions
function showSuccess(message, title = 'Thành Công!') {
    showNotification('success', message, title);
}

function showError(message, title = 'Có Lỗi Xảy Ra!') {
    showNotification('error', message, title);
}

function showWarning(message, title = 'Cảnh Báo!') {
    showNotification('warning', message, title);
}

function showInfo(message, title = 'Thông Tin') {
    showNotification('info', message, title);
}
</script>
