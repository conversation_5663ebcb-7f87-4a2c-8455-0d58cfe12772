# Admin Panel Setup Guide

## Default Admin Login Credentials

After running the database migrations and seeders, you can use these default admin accounts to login:

### Primary Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Name**: BS Lý Đại Lương

### System Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Name**: Admin System

### Demo Admin Account (for testing)
- **Email**: `<EMAIL>`
- **Password**: `demo123456`
- **Name**: Demo Admin

## Setup Instructions

1. **Run Migrations**
   ```bash
   php artisan migrate
   ```

2. **Run Seeders**
   ```bash
   php artisan db:seed
   ```
   
   Or run only the admin seeder:
   ```bash
   php artisan db:seed --class=AdminSeeder
   ```

3. **Access Admin Panel**
   - Navigate to: `http://your-domain.com/admin/login`
   - Use any of the credentials above to login

## Admin Panel Features

### Dashboard
- Overview statistics for appointments, questions, news, and videos
- Recent activity lists
- Weekly charts for appointments and questions
- Monthly comparison statistics

### Appointments Management
- View all appointments with filtering and search
- Create new appointments
- Edit existing appointments
- Change appointment status (pending, confirmed, cancelled)
- Add admin notes

### Questions Management
- View all questions with filtering
- Answer questions
- Mark questions as answered
- Search through questions

### News Management
- Create and edit news articles
- Upload images for articles
- Set publication status (draft/published)
- Categorize articles
- Auto-generate slugs and excerpts

### Videos Management
- Add YouTube videos
- Auto-extract video thumbnails
- Set featured videos
- Categorize videos
- Track video views

### Settings Management
- Site configuration (name, description, logo, favicon)
- Contact information
- Social media links
- Content management (about, services)
- SEO settings (meta tags, analytics)

## Security Notes

**IMPORTANT**: Please change the default passwords immediately after first login for security purposes.

1. Login with default credentials
2. Go to Settings or create a password change feature
3. Update passwords to strong, unique passwords
4. Consider removing demo accounts in production

## File Permissions

Make sure the following directories are writable:
- `storage/app/`
- `storage/logs/`
- `public/storage/` (create symlink: `php artisan storage:link`)

## Additional Configuration

### Email Configuration
Update your `.env` file with proper email settings for appointment notifications:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="BS Lý Đại Lương"
```

### Storage Link
Create a symbolic link for file uploads:
```bash
php artisan storage:link
```

### Cache Configuration
For better performance in production:
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```
